# Python视频生成工具

这是一个用Python重写的视频生成工具，替代了原来的Java + FFmpeg方案，使用更现代的Python库来处理视频生成。

## 🚀 主要特性

- **纯Python实现**：使用moviepy、PIL等成熟的Python库
- **更好的集成**：避免了FFmpeg的外部依赖和兼容性问题
- **智能字幕**：自动添加中文字幕，支持多种字体
- **音频循环**：自动循环背景音乐到指定时长
- **高质量输出**：支持多种视频格式和编码选项

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

## 🎯 核心功能

### 1. 媒体资源处理
- **图片处理**：自动下载、调整尺寸、添加字幕
- **视频处理**：调整尺寸、帧率、时长
- **音频处理**：循环拼接、时长控制

### 2. 字幕系统
- **全局字幕**：标题 + 内容描述
- **单独字幕**：每个媒体资源的独立字幕
- **中文支持**：自动检测系统字体，支持中文显示
- **样式控制**：字体大小、颜色、边框

### 3. 音频处理
- **背景音乐循环**：自动循环到主视频时长
- **音频拼接**：主视频音频 + 结尾视频音频
- **格式统一**：自动处理音频格式兼容性

## 📝 使用方法

### 基本使用

```python
from video_generator import VideoGenerator, MediaResource, GlobalSubtitle

# 创建生成器
generator = VideoGenerator()

# 定义媒体资源
media_resources = [
    MediaResource(
        type=1,  # 1=图片, 2=视频
        order=1,
        url="https://example.com/image1.jpg",
        subtitle="图片描述"
    ),
    # ... 更多资源
]

# 定义全局字幕
global_subtitle = GlobalSubtitle(
    title="视频标题",
    content="视频描述内容"
)

# 生成视频
result_path = generator.generate_video(
    media_resources=media_resources,
    background_audio_path="background.mp3",
    end_video_path="ending.mp4",
    output_path="output.mp4",
    global_subtitle=global_subtitle
)
```

### 运行示例

```bash
python video_generator.py
```

## 🔧 配置选项

### VideoGenerator 配置

```python
class VideoGenerator:
    DEFAULT_IMAGE_DURATION = 8  # 默认图片显示时长（秒）
    VIDEO_SIZE = (720, 1280)    # 视频尺寸 (宽, 高)
    VIDEO_FPS = 30              # 视频帧率
```

### 字幕配置

- **标题字体**：48px，白色，黑色边框
- **内容字体**：32px，白色，黑色边框
- **字幕字体**：36px，白色，黑色边框

## 🆚 与Java版本对比

| 特性 | Java + FFmpeg | Python + MoviePy |
|------|---------------|-------------------|
| **依赖管理** | 复杂（FFmpeg外部依赖） | 简单（pip安装） |
| **跨平台** | 需要FFmpeg二进制 | 纯Python，跨平台 |
| **错误处理** | FFmpeg错误难调试 | Python异常清晰 |
| **像素格式** | 兼容性问题 | 自动处理 |
| **中文字幕** | 转义复杂 | 原生支持 |
| **开发效率** | 低（命令行拼接） | 高（面向对象） |
| **性能** | 高（C++实现） | 中等（Python） |

## 🐛 问题解决

### 常见问题

1. **字体问题**
   ```
   解决：自动检测系统字体，支持Windows/macOS/Linux
   ```

2. **内存使用**
   ```
   解决：自动清理临时文件和视频对象
   ```

3. **网络下载**
   ```
   解决：添加超时和重试机制
   ```

## 📋 关键优势

### 🎯 **解决了Java版本的所有问题**

1. **✅ 像素格式兼容性**：MoviePy自动处理，无需手动转换
2. **✅ 中文字幕支持**：PIL原生支持中文，无需复杂转义
3. **✅ 音频循环控制**：精确控制音频时长和循环
4. **✅ 依赖管理简单**：pip安装，无需外部二进制
5. **✅ 错误调试清晰**：Python异常堆栈，易于定位问题

### 🚀 **预期效果**

使用这个Python版本，您应该能得到：
- **前48秒**：图片视频 + 循环背景音乐（精确控制）
- **后3秒**：结尾视频 + 结尾音乐
- **完美字幕**：中文字幕正确显示
- **无兼容性问题**：跨平台运行

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！
