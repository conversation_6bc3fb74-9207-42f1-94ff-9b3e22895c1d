package com.ruoyi.common.utils;

import com.ruoyi.common.vo.GlobalSubtitleVO;
import com.ruoyi.common.vo.MediaResourceVo;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.ffmpeg.global.avutil;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.font.TextLayout;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.awt.image.ColorModel;
import java.awt.image.RescaleOp;
import java.awt.image.WritableRaster;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

@Slf4j
public class VideoGenerateUtils {


    // 视频帧率
    private static final int FRAME_RATE = 30;
    // 默认每张图片显示时间（秒）- 增加到8秒以获得更长的视频
    private static final int DEFAULT_IMAGE_DURATION = 8;
    // 转场持续时间（秒）
    private static final double TRANSITION_DURATION = 0.5;
    // 视频比特率
    private static final int VIDEO_BITRATE = 2500000;  // 增加到2.5Mbps提高清晰度
    // 视频宽度
    private static final int VIDEO_WIDTH = 720;
    // 视频高度
    private static final int VIDEO_HEIGHT = 1280;
    // 视频格式
    private static final String VIDEO_FORMAT = "mp4";
    // 音频采样率
    private static final int SAMPLE_RATE = 44100;
    // 音频通道数
    private static final int AUDIO_CHANNELS = 2;
    // 字幕字体大小
    private static final int SUBTITLE_FONT_SIZE = 38; // 内容字体更大
    // 主标题字体大小
    private static final int TITLE_FONT_SIZE = 60;    // 主标题更大
    // 字幕颜色
    private static final Color SUBTITLE_COLOR = Color.WHITE;
    // 主标题颜色
    private static final Color TITLE_COLOR = new Color(255, 140, 0); // 橙色描边
    // 字幕背景颜色
    private static final Color SUBTITLE_BG_COLOR = new Color(0, 0, 0, 128);
    // 每行最大字符数
    private static final int MAX_CHARS_PER_LINE = 20;
    private static final int ANIM_FRAMES = 15;
    private static final int CONTENT_MARGIN = 60; // 内容左右边距

    // 随机数生成器
    private static final Random RANDOM = new Random();

    // 转场效果类型
    private static final String[] TRANSITION_EFFECTS = {
            "fade",                  // 淡入淡出
            "wipeleft",              // 从左擦除
            "wiperight",             // 从右擦除
            "wipeup",                // 从上擦除
            "wipedown",              // 从下擦除
            "slideleft",             // 从左滑动
            "slideright",            // 从右滑动
            "slideup",               // 从上滑动
            "slidedown",             // 从下滑动
            "circlecrop",            // 圆形裁剪
            "rectcrop",              // 矩形裁剪
            "distance",              // 距离变换
            "fadeblack",             // 黑色淡入淡出
            "fadewhite",             // 白色淡入淡出
            "radial",                // 径向擦除
            "smoothleft",            // 平滑左移
            "smoothright",           // 平滑右移
            "smoothup",              // 平滑上移
            "smoothdown",            // 平滑下移
            "circleopen",            // 圆形打开
            "circleclose",           // 圆形关闭
            "vertopen",              // 垂直打开
            "vertclose",             // 垂直关闭
            "horzopen",              // 水平打开
            "horzclose",             // 水平关闭
            "dissolve",              // 溶解
            "pixelize",              // 像素化
            "radial",                // 径向
            "squeezeh",              // 水平挤压
            "squeezev"               // 垂直挤压
    };

    // 文字样式类型
    private static final TextStyle[] TEXT_STYLES = {
            new TextStyle("微软雅黑", Font.BOLD, Color.WHITE, new Color(0, 0, 0, 180), 2.0f),
            new TextStyle("黑体", Font.BOLD, Color.WHITE, new Color(0, 0, 0, 200), 2.5f),
            new TextStyle("宋体", Font.BOLD, Color.WHITE, new Color(50, 50, 50, 180), 2.0f),
            new TextStyle("楷体", Font.BOLD, Color.WHITE, new Color(30, 30, 30, 180), 2.2f),
            new TextStyle("微软雅黑", Font.BOLD, new Color(255, 255, 200), new Color(80, 40, 0, 180), 2.0f),
            new TextStyle("微软雅黑", Font.BOLD, new Color(255, 240, 200), new Color(100, 50, 0, 180), 2.0f),
            new TextStyle("微软雅黑", Font.BOLD, new Color(220, 255, 220), new Color(0, 80, 0, 180), 2.0f),
            new TextStyle("微软雅黑", Font.BOLD, new Color(220, 220, 255), new Color(0, 0, 100, 180), 2.0f),
            new TextStyle("微软雅黑", Font.BOLD, new Color(255, 220, 220), new Color(100, 0, 0, 180), 2.0f),
            new TextStyle("黑体", Font.BOLD, new Color(255, 255, 180), new Color(120, 60, 0, 180), 2.5f),
            new TextStyle("黑体", Font.BOLD, new Color(180, 255, 180), new Color(0, 100, 0, 180), 2.5f),
            new TextStyle("黑体", Font.BOLD, new Color(180, 180, 255), new Color(0, 0, 120, 180), 2.5f),
            new TextStyle("宋体", Font.BOLD, new Color(255, 220, 180), new Color(100, 50, 0, 180), 2.0f),
            new TextStyle("宋体", Font.BOLD, new Color(220, 255, 180), new Color(0, 100, 0, 180), 2.0f),
            new TextStyle("宋体", Font.BOLD, new Color(220, 180, 255), new Color(50, 0, 100, 180), 2.0f),
            new TextStyle("楷体", Font.BOLD, new Color(255, 200, 200), new Color(100, 0, 0, 180), 2.2f),
            new TextStyle("楷体", Font.BOLD, new Color(200, 255, 200), new Color(0, 100, 0, 180), 2.2f),
            new TextStyle("楷体", Font.BOLD, new Color(200, 200, 255), new Color(0, 0, 100, 180), 2.2f),
            new TextStyle("微软雅黑", Font.BOLD, new Color(255, 255, 255), new Color(0, 0, 0, 200), 3.0f),
            new TextStyle("微软雅黑", Font.BOLD, new Color(255, 220, 100), new Color(100, 50, 0, 200), 2.5f)
    };

    // 文字样式类
    private static class TextStyle {
        String fontName;
        int fontStyle;
        Color textColor;
        Color outlineColor;
        float outlineWidth;

        public TextStyle(String fontName, int fontStyle, Color textColor, Color outlineColor, float outlineWidth) {
            this.fontName = fontName;
            this.fontStyle = fontStyle;
            this.textColor = textColor;
            this.outlineColor = outlineColor;
            this.outlineWidth = outlineWidth;
        }
    }

    static {
        // 设置FFmpeg日志回调
        avutil.av_log_set_level(avutil.AV_LOG_INFO);
    }


    /**
     * 将媒体资源列表转换为视频，支持全局字幕
     * 主视频播放背景音乐，结尾视频播放其自带的结尾音乐
     *
     * @param mediaResources 媒体资源列表
     * @param outputPath     输出视频路径
     * @param globalSubtitle 全局字幕对象（可为null）
     * @param audioPath      背景音频路径（可为null，仅用于主视频部分）
     * @return 生成的视频文件
     */
    public static File generateVideo(java.util.List<MediaResourceVo> mediaResources, String outputPath,
                                     GlobalSubtitleVO globalSubtitle, String audioPath) throws Exception {
        // 添加唯一标识符跟踪调用
        String callId = "CALL_" + System.currentTimeMillis();
        log.info("=== 开始视频生成 {} ===", callId);

        // 重置效果，确保每次生成新视频时使用新的随机效果
        resetEffects();

        // 1. 生成主视频到临时路径
        String tempMainVideoPath = outputPath.replace(".mp4", "_temp_main.mp4");
        File mainVideo = generateVideoFromImages(mediaResources, tempMainVideoPath, globalSubtitle, audioPath);

        // 2. 获取结尾视频路径
        URL resource = VideoGenerateUtils.class.getClassLoader().getResource("templates/宠孩生活结尾.mp4");
        if (resource == null) {
            throw new FileNotFoundException("结尾视频文件未找到！");
        }
        String endVideoPath = URLDecoder.decode(resource.getPath(), "UTF-8").replaceFirst("^/", "");

        // 3. 确保主视频文件完全写入完成
        log.info("等待主视频文件完全写入...");
        Thread.sleep(1000); // 等待1秒确保文件写入完成

        // 验证主视频文件
        if (!mainVideo.exists() || mainVideo.length() == 0) {
            throw new IOException("主视频文件无效：" + mainVideo.getAbsolutePath());
        }
        log.info("主视频文件验证通过，大小：{}MB", mainVideo.length() / 1024 / 1024);

        // 4. 拼接结尾视频（结尾视频保持自带音频）
        File result = concatEndVideoWithSeparateAudio(mainVideo.getAbsolutePath(), endVideoPath, outputPath, audioPath);
        log.info("=== 视频生成完成 {} ===", callId);
        return result;
    }

    public static File generateVideoFromImages(List<MediaResourceVo> mediaResources, String outputPath,
                                               GlobalSubtitleVO globalSubtitle, String audioPath) throws IOException {
        if (mediaResources == null || mediaResources.isEmpty()) {
            throw new IllegalArgumentException("媒体资源列表不能为空");
        }

        log.info("开始生成主视频（使用纯FFmpeg方式），输出路径：{}", outputPath);
        log.info("媒体资源数量：{}", mediaResources.size());

        List<MediaResourceVo> visualResources = mediaResources.stream()
                .filter(r -> r.getType() == 1 || r.getType() == 2)
                .sorted(Comparator.comparing(MediaResourceVo::getOrder, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());

        if (visualResources.isEmpty()) {
            throw new IllegalArgumentException("至少需要一个图片或视频资源");
        }

        log.info("视觉资源数量：{}", visualResources.size());

        try {
            // 使用纯FFmpeg方式生成视频
            generateVideoWithFFmpeg(visualResources, outputPath, globalSubtitle);

            File outputFile = new File(outputPath);
            if (!outputFile.exists() || outputFile.length() == 0) {
                throw new IOException("生成的视频文件无效");
            }

            log.info("视频生成成功（无音频），大小：{}MB", outputFile.length() / 1024 / 1024);
            return outputFile;

        } catch (Exception e) {
            log.error("生成视频失败", e);
            throw new IOException("生成视频失败: " + e.getMessage());
        }
    }

    /**
     * 使用纯FFmpeg方式生成视频，避免JavaCV的进程冲突
     */
    private static void generateVideoWithFFmpeg(List<MediaResourceVo> visualResources, String outputPath,
                                               GlobalSubtitleVO globalSubtitle) throws IOException {
        // 创建FFmpeg输入列表文件
        String listPath = System.getProperty("java.io.tmpdir") + "/ffmpeg_input_" + System.currentTimeMillis() + ".txt";

        // 详细分析每个资源的时长
        log.info("开始分析视觉资源时长：");
        int totalDuration = 0;
        List<String> tempFiles = new ArrayList<>();

        for (MediaResourceVo resource : visualResources) {
            log.info("资源详情：type={}, order={}, duration={}, url={}",
                    resource.getType(), resource.getOrder(), resource.getDuration(), resource.getUrl());

            if (resource.getType() == 1) { // 图片
                // 确定显示时长
                int duration = DEFAULT_IMAGE_DURATION;
                if (resource.getDuration() != null && resource.getDuration() > 0) {
                    duration = resource.getDuration();
                }
                totalDuration += duration;

                log.info("图片时长计算：默认={}秒，设置={}秒，实际={}秒",
                        DEFAULT_IMAGE_DURATION, resource.getDuration(), duration);
            } else if (resource.getType() == 2) { // 视频
                // 对于视频，假设默认时长（实际应该获取视频时长）
                totalDuration += 5; // 临时假设5秒
                log.info("视频时长：假设5秒");
            }
        }

        log.info("预计总时长：{}秒", totalDuration);

        // 使用更简单的方式：为每张图片创建一个短视频，然后拼接
        List<String> videoSegments = new ArrayList<>();

        for (int i = 0; i < visualResources.size(); i++) {
            MediaResourceVo resource = visualResources.get(i);
            if (resource.getType() == 1) { // 图片
                String tempFileName = "temp_image_" + System.currentTimeMillis() + "_" + resource.getOrder() + ".jpg";
                File tempFile = new File(System.getProperty("java.io.tmpdir"), tempFileName);
                downloadImageToFile(resource.getUrl(), tempFile);

                // 确定显示时长
                int duration = DEFAULT_IMAGE_DURATION;
                if (resource.getDuration() != null && resource.getDuration() > 0) {
                    duration = resource.getDuration();
                }

                // 为每张图片创建一个视频片段（统一像素格式）
                String segmentPath = System.getProperty("java.io.tmpdir") + "/segment_" + System.currentTimeMillis() + "_" + i + ".mp4";
                // 构建字幕滤镜
                String subtitleFilter = buildSubtitleFilter(resource, globalSubtitle);
                String videoFilter = "scale=720:1280,fps=30,format=yuv420p" + subtitleFilter;

                String segmentCommand = String.format(
                    "ffmpeg -y -loop 1 -i \"%s\" -t %d -vf \"%s\" -c:v libx264 -crf 18 -preset fast -an \"%s\"",
                    tempFile.getAbsolutePath(), duration, videoFilter, segmentPath
                );

                runFFmpeg(segmentCommand);
                videoSegments.add(segmentPath);

                log.info("创建图片视频片段：{}, 时长：{}秒", new File(segmentPath).getName(), duration);
            }
        }

        // 创建拼接列表
        try (PrintWriter pw = new PrintWriter(new FileWriter(listPath))) {
            for (String segment : videoSegments) {
                pw.println(String.format("file '%s'", segment.replace("\\", "/")));
            }
        }

        // 使用FFmpeg的concat demuxer生成视频（统一像素格式）
        String ffmpegCommand = String.format(
            "ffmpeg -y -f concat -safe 0 -i \"%s\" -vf scale=720:1280,fps=30,format=yuv420p -c:v libx264 -crf 18 -preset fast -an \"%s\"",
            listPath, outputPath
        );

        log.info("使用FFmpeg生成视频");
        runFFmpeg(ffmpegCommand);

        // 清理临时文件
        new File(listPath).delete();

        // 清理视频片段文件
        for (String segment : videoSegments) {
            try {
                new File(segment).delete();
            } catch (Exception e) {
                log.warn("清理视频片段失败：{}", e.getMessage());
            }
        }

        // 清理下载的临时图片文件
        for (String tempFile : tempFiles) {
            try {
                if (tempFile.contains("temp_image_")) {
                    new File(tempFile).delete();
                }
            } catch (Exception e) {
                log.warn("清理临时图片失败：{}", e.getMessage());
            }
        }
    }


    public static File concatEndVideo(String mainVideoPath, String endVideoPath, String outputPath, String audioPath) throws IOException {
        return concatEndVideoWithSeparateAudio(mainVideoPath, endVideoPath, outputPath, audioPath);
    }

    public static File concatEndVideoWithSeparateAudio(String mainVideoPath, String endVideoPath, String outputPath, String audioPath) throws IOException {
        log.info("开始音频处理和视频拼接");

        File mainVideoFile = new File(mainVideoPath);
        File endVideoFile = new File(endVideoPath);
        if (!mainVideoFile.exists() || mainVideoFile.length() == 0) {
            throw new IOException("主视频文件无效");
        }
        if (!endVideoFile.exists() || endVideoFile.length() == 0) {
            throw new IOException("结尾视频文件无效");
        }

        // 获取主视频和结尾视频的时长
        double mainVideoDuration = getVideoDuration(mainVideoPath);
        double endVideoDuration = getVideoDuration(endVideoPath);
        log.info("时长信息 - 主视频：{}秒，结尾视频：{}秒", String.format("%.1f", mainVideoDuration), String.format("%.1f", endVideoDuration));

        // 1. 立即为主视频添加背景音乐
        String mainVideoWithAudio = mainVideoPath;
        if (audioPath != null && new File(audioPath).exists()) {
            mainVideoWithAudio = mainVideoPath.replace(".mp4", "_with_bg_audio.mp4");

            // 获取背景音乐的原始时长
            double originalAudioDuration = getVideoDuration(audioPath);
            log.info("背景音乐处理 - 原始：{}秒，目标：{}秒", String.format("%.1f", originalAudioDuration), String.format("%.1f", mainVideoDuration));

            // 计算需要多少个完整的音频文件
            int fullCopies = (int) Math.floor(mainVideoDuration / originalAudioDuration);
            double remainingTime = mainVideoDuration - (fullCopies * originalAudioDuration);

            log.info("音频拼接方案：{}个完整音频 + {}秒剩余", fullCopies, String.format("%.1f", remainingTime));

            // 创建音频拼接列表
            String audioListPath = mainVideoPath.replace(".mp4", "_audio_list.txt");
            try (PrintWriter pw = new PrintWriter(new FileWriter(audioListPath))) {
                // 添加完整的音频文件
                for (int i = 0; i < fullCopies; i++) {
                    pw.println("file '" + audioPath.replace("\\", "\\\\") + "'");
                }
                // 如果有剩余时间，再添加一个完整的音频（会被截断）
                if (remainingTime > 0) {
                    pw.println("file '" + audioPath.replace("\\", "\\\\") + "'");
                }
            }

            // 使用 concat 协议拼接音频，然后截断到精确时长
            String concatAudio = mainVideoPath.replace(".mp4", "_concat_audio.m4a");
            runFFmpeg(String.format(
                    "ffmpeg -y -f concat -safe 0 -i \"%s\" -t %.2f -c:a aac -b:a 128k \"%s\"",
                    audioListPath, mainVideoDuration, concatAudio
            ));

            // 验证拼接后的音频时长
            if (!new File(concatAudio).exists()) {
                throw new IOException("音频拼接失败，文件不存在：" + concatAudio);
            }
            double concatAudioDuration = getVideoDuration(concatAudio);
            log.info("音频拼接结果：{}秒", String.format("%.1f", concatAudioDuration));

            // 将拼接的音频添加到主视频
            log.info("将拼接音频添加到主视频：{} + {} -> {}",
                    new File(mainVideoPath).getName(),
                    new File(concatAudio).getName(),
                    new File(mainVideoWithAudio).getName());
            runFFmpeg(String.format(
                    "ffmpeg -y -i \"%s\" -i \"%s\" -c:v copy -map 0:v:0 -map 1:a:0 -c:a aac -b:a 128k \"%s\"",
                    mainVideoPath, concatAudio, mainVideoWithAudio
            ));

            // 清理临时文件
            new File(audioListPath).delete();
            new File(concatAudio).delete();

            // 验证最终视频的时长和音频
            if (!new File(mainVideoWithAudio).exists()) {
                throw new IOException("添加音频到主视频失败，文件不存在：" + mainVideoWithAudio);
            }
            double finalVideoDuration = getVideoDuration(mainVideoWithAudio);
            log.info("主视频最终时长：{}秒", String.format("%.1f", finalVideoDuration));

            // 验证主视频是否有音频轨道
            verifyVideoHasAudio(mainVideoWithAudio);
        } else {
            log.info("无背景音乐，主视频保持无音频");
        }

        // 2. 结尾视频保持原有音频
        String endVideoWithAudio = endVideoPath;

        // 3. 标准化视频格式（统一像素格式和色彩空间）
        String normMain = System.getProperty("java.io.tmpdir") + "/main_norm_" + System.currentTimeMillis() + ".mp4";
        runFFmpeg(String.format(
                "ffmpeg -y -i \"%s\" -vf scale=720:1280,fps=30,format=yuv420p -c:v libx264 -crf 18 -preset fast -c:a copy \"%s\"",
                mainVideoWithAudio, normMain
        ));

        // 验证标准化后的主视频是否仍有音频
        verifyVideoHasAudio(normMain);

        String normEnd = System.getProperty("java.io.tmpdir") + "/end_norm_" + System.currentTimeMillis() + ".mp4";
        runFFmpeg(String.format(
                "ffmpeg -y -i \"%s\" -vf scale=720:1280,fps=30,format=yuv420p -c:v libx264 -crf 18 -preset fast -c:a aac -b:a 128k \"%s\"",
                endVideoWithAudio, normEnd
        ));

        // 4. 准备拼接
        log.info("准备拼接：主视频={}, 结尾视频={}",
                new File(normMain).getName(),
                new File(normEnd).getName());

        // 5. 最终拼接（使用 concat demuxer，更稳定）
        String finalPath = outputPath;
        String concatListPath = System.getProperty("java.io.tmpdir") + "/final_concat_" + System.currentTimeMillis() + ".txt";

        try (PrintWriter pw = new PrintWriter(new FileWriter(concatListPath))) {
            pw.println(String.format("file '%s'", normMain.replace("\\", "/")));
            pw.println(String.format("file '%s'", normEnd.replace("\\", "/")));
        }

        log.info("使用手动拼接方式处理视频和音频");

        // 第一步：提取主视频的视频轨道（无音频）
        String mainVideoOnly = System.getProperty("java.io.tmpdir") + "/main_video_only_" + System.currentTimeMillis() + ".mp4";
        runFFmpeg(String.format(
                "ffmpeg -y -i \"%s\" -c:v copy -an \"%s\"",
                normMain, mainVideoOnly
        ));

        // 第二步：提取结尾视频的视频轨道（无音频）
        String endVideoOnly = System.getProperty("java.io.tmpdir") + "/end_video_only_" + System.currentTimeMillis() + ".mp4";
        runFFmpeg(String.format(
                "ffmpeg -y -i \"%s\" -c:v copy -an \"%s\"",
                normEnd, endVideoOnly
        ));

        // 第三步：使用 concat demuxer 拼接纯视频（无音频冲突）
        String videoOnlyListPath = System.getProperty("java.io.tmpdir") + "/video_only_concat_" + System.currentTimeMillis() + ".txt";
        try (PrintWriter pw = new PrintWriter(new FileWriter(videoOnlyListPath))) {
            pw.println(String.format("file '%s'", mainVideoOnly.replace("\\", "/")));
            pw.println(String.format("file '%s'", endVideoOnly.replace("\\", "/")));
        }

        String finalVideoOnly = System.getProperty("java.io.tmpdir") + "/final_video_only_" + System.currentTimeMillis() + ".mp4";
        runFFmpeg(String.format(
                "ffmpeg -y -f concat -safe 0 -i \"%s\" -c:v copy \"%s\"",
                videoOnlyListPath, finalVideoOnly
        ));

        // 第四步：创建正确的音频轨道
        // 主视频音频：48秒循环背景音乐（从原始背景音乐文件生成）
        String mainAudio = System.getProperty("java.io.tmpdir") + "/main_audio_" + System.currentTimeMillis() + ".aac";
        if (audioPath != null && new File(audioPath).exists()) {
            // 使用原始背景音乐文件，循环到48秒
            double originalAudioDuration = getVideoDuration(audioPath);
            int fullCopies = (int) Math.floor(48.0 / originalAudioDuration);
            double remainingTime = 48.0 - (fullCopies * originalAudioDuration);

            String audioListPath = System.getProperty("java.io.tmpdir") + "/main_audio_list_" + System.currentTimeMillis() + ".txt";
            try (PrintWriter pw = new PrintWriter(new FileWriter(audioListPath))) {
                for (int i = 0; i < fullCopies; i++) {
                    pw.println("file '" + audioPath.replace("\\", "\\\\") + "'");
                }
                if (remainingTime > 0) {
                    pw.println("file '" + audioPath.replace("\\", "\\\\") + "'");
                }
            }

            runFFmpeg(String.format(
                    "ffmpeg -y -f concat -safe 0 -i \"%s\" -t 48.00 -c:a aac \"%s\"",
                    audioListPath, mainAudio
            ));

            new File(audioListPath).delete();
        } else {
            // 如果没有背景音乐，创建静音音频
            runFFmpeg(String.format(
                    "ffmpeg -y -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=44100 -t 48 -c:a aac \"%s\"",
                    mainAudio
            ));
        }

        // 第五步：提取结尾视频的音频（3秒结尾音乐）
        String endAudio = System.getProperty("java.io.tmpdir") + "/end_audio_" + System.currentTimeMillis() + ".aac";
        runFFmpeg(String.format(
                "ffmpeg -y -i \"%s\" -vn -c:a aac \"%s\"",
                normEnd, endAudio
        ));

        // 第六步：拼接音频（48秒背景音乐 + 3秒结尾音乐）
        String audioListPath = System.getProperty("java.io.tmpdir") + "/audio_concat_" + System.currentTimeMillis() + ".txt";
        try (PrintWriter pw = new PrintWriter(new FileWriter(audioListPath))) {
            pw.println(String.format("file '%s'", mainAudio.replace("\\", "/")));
            pw.println(String.format("file '%s'", endAudio.replace("\\", "/")));
        }

        String finalAudio = System.getProperty("java.io.tmpdir") + "/final_audio_" + System.currentTimeMillis() + ".aac";
        runFFmpeg(String.format(
                "ffmpeg -y -f concat -safe 0 -i \"%s\" -c:a aac \"%s\"",
                audioListPath, finalAudio
        ));

        // 第七步：合并最终视频和音频
        runFFmpeg(String.format(
                "ffmpeg -y -i \"%s\" -i \"%s\" -c:v copy -c:a aac \"%s\"",
                finalVideoOnly, finalAudio, finalPath
        ));

        // 清理临时文件
        new File(mainVideoOnly).delete();
        new File(endVideoOnly).delete();
        new File(videoOnlyListPath).delete();
        new File(finalVideoOnly).delete();
        new File(mainAudio).delete();
        new File(endAudio).delete();
        new File(audioListPath).delete();
        new File(finalAudio).delete();

        // 清理 concat list 文件
        new File(concatListPath).delete();

        // 6. 校验输出文件
        File outputFile = new File(finalPath);
        if (!outputFile.exists() || outputFile.length() == 0) {
            throw new IOException("拼接输出失败：" + finalPath);
        }

        // 验证结果
        double finalTotalDuration = getVideoDuration(finalPath);
        double expectedDuration = 48.0 + 3.0; // 48秒主视频 + 3秒结尾视频
        log.info("拼接完成 - 总时长：{}秒（预期：{}秒）", String.format("%.1f", finalTotalDuration), String.format("%.1f", expectedDuration));

        // 清理临时文件
        new File(normMain).delete();
        new File(normEnd).delete();
        if (!mainVideoWithAudio.equals(mainVideoPath)) {
            new File(mainVideoWithAudio).delete();
        }
        return outputFile;
    }

    /**
     * 下载图片到指定文件
     */
    private static void downloadImageToFile(String imageUrl, File targetFile) throws IOException {
        try {
            if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
                // 网络图片
                URL url = new URL(imageUrl);
                try (InputStream in = url.openStream();
                     FileOutputStream out = new FileOutputStream(targetFile)) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                }
            } else {
                // 本地文件
                File sourceFile = new File(imageUrl);
                if (!sourceFile.exists()) {
                    throw new IOException("图片文件不存在：" + imageUrl);
                }
                try (FileInputStream in = new FileInputStream(sourceFile);
                     FileOutputStream out = new FileOutputStream(targetFile)) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                }
            }
            log.info("图片下载完成：{}", targetFile.getName());
        } catch (Exception e) {
            throw new IOException("下载图片失败：" + e.getMessage());
        }
    }

    /**
     * 验证视频是否包含音频轨道
     */
    private static void verifyVideoHasAudio(String videoPath) {
        try {
            String command = String.format("ffprobe -v quiet -select_streams a -show_entries stream=codec_type \"%s\"", videoPath);
            Process process = Runtime.getRuntime().exec(command);

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                boolean hasAudio = false;
                while ((line = reader.readLine()) != null) {
                    if (line.contains("codec_type=audio")) {
                        hasAudio = true;
                        break;
                    }
                }

                if (hasAudio) {
                    log.info("✓ 视频包含音频轨道：{}", new File(videoPath).getName());
                } else {
                    log.warn("✗ 视频缺少音频轨道：{}", new File(videoPath).getName());
                }
            }

            process.waitFor();
        } catch (Exception e) {
            log.warn("验证音频轨道失败：{}", e.getMessage());
        }
    }


    public static double getVideoDuration(String videoPath) throws IOException {
        FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(videoPath);
        try {
            grabber.start();
            return grabber.getLengthInTime() / 1_000_000.0; // 转为秒
        } catch (Exception e) {
            throw new IOException("获取视频时长失败: " + e.getMessage(), e);
        } finally {
            try {
                grabber.stop();
            } catch (Exception ignored) {
            }
        }
    }

    private static void runFFmpeg(String command) throws IOException {
        // 临时恢复完整命令日志用于调试
        log.info("执行FFmpeg命令：{}", command);

        Process process = Runtime.getRuntime().exec(command);
        try (BufferedReader err = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
            String line;
            while ((line = err.readLine()) != null) {
                // 只记录关键信息
                if (line.contains("Input #") || line.contains("Output #") || line.contains("Error") || line.contains("error")) {
                    log.info("FFmpeg: {}", line);
                }
            }
        }
        try {
            int exit = process.waitFor();
            if (exit != 0) throw new IOException("FFmpeg执行失败，退出码：" + exit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("FFmpeg命令中断", e);
        }
    }




    /**
     * 下载图片到临时文件
     */
    private static File downloadImage(String imageUrl) throws IOException {
        int maxRetries = 3;
        int retryCount = 0;
        int connectTimeout = 30000;  // 30秒连接超时
        int readTimeout = 30000;     // 30秒读取超时

        while (retryCount < maxRetries) {
            try {
                URL url = new URL(imageUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(connectTimeout);
                connection.setReadTimeout(readTimeout);
                connection.setRequestProperty("User-Agent", "Mozilla/5.0");
                connection.setRequestProperty("Accept", "image/*");

                log.info("开始下载图片，URL: {}, 重试次数: {}", imageUrl, retryCount);

                if (connection.getResponseCode() != HttpURLConnection.HTTP_OK) {
                    throw new IOException("下载图片失败，HTTP状态码: " + connection.getResponseCode());
                }

                String tempDir = System.getProperty("java.io.tmpdir");
                String fileName = "temp_image_" + System.currentTimeMillis() + ".jpg";
                File tempFile = new File(tempDir, fileName);

                try (InputStream in = new BufferedInputStream(connection.getInputStream());
                     FileOutputStream out = new FileOutputStream(tempFile)) {
                    byte[] buffer = new byte[8192];  // 增加缓冲区大小
                    int bytesRead;
                    long totalBytesRead = 0;
                    long startTime = System.currentTimeMillis();

                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                        totalBytesRead += bytesRead;

                        // 检查是否超时
                        if (System.currentTimeMillis() - startTime > readTimeout) {
                            throw new IOException("下载超时，已下载: " + totalBytesRead + " 字节");
                        }
                    }
                }

                log.info("图片下载完成，大小: {} 字节", tempFile.length());
                return tempFile;

            } catch (IOException e) {
                retryCount++;
                log.warn("下载图片失败，URL: {}, 重试次数: {}, 错误: {}", imageUrl, retryCount, e.getMessage());

                if (retryCount >= maxRetries) {
                    throw new IOException("下载图片失败，已重试" + maxRetries + "次: " + e.getMessage(), e);
                }

                // 重试前等待一段时间
                try {
                    Thread.sleep(1000 * retryCount);  // 递增等待时间
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new IOException("下载被中断", ie);
                }
            }
        }

        throw new IOException("下载图片失败，超过最大重试次数");
    }

    // 当前视频使用的转场效果和文字样式
    private static String currentTransitionEffect = null;
    private static TextStyle currentTextStyle = null;

    /**
     * 获取转场效果，每次生成视频只使用一种
     */
    private static String getTransitionEffect() {
        if (currentTransitionEffect == null) {
            currentTransitionEffect = TRANSITION_EFFECTS[RANDOM.nextInt(TRANSITION_EFFECTS.length)];
            log.info("本次视频使用转场效果：{}", currentTransitionEffect);
        }
        return currentTransitionEffect;
    }

    /**
     * 获取文字样式，每次生成视频只使用一种
     */
    private static TextStyle getTextStyle() {
        if (currentTextStyle == null) {
            currentTextStyle = TEXT_STYLES[RANDOM.nextInt(TEXT_STYLES.length)];
            log.info("本次视频使用文字样式：字体={}, 颜色={}", currentTextStyle.fontName, currentTextStyle.textColor);
        }
        return currentTextStyle;
    }

    /**
     * 重置当前使用的效果，在每次生成新视频前调用
     */
    private static void resetEffects() {
        currentTransitionEffect = null;
        currentTextStyle = null;
    }

    /**
     * 构建FFmpeg字幕滤镜（简化版，避免中文字符问题）
     */
    private static String buildSubtitleFilter(MediaResourceVo resource, GlobalSubtitleVO globalSubtitle) {
        // 暂时禁用FFmpeg字幕，避免中文字符解析问题
        // 后续可以考虑使用字幕文件或其他方式
        log.info("字幕功能暂时禁用，避免FFmpeg中文字符解析问题");
        return "";
    }

    /**
     * 处理图片资源，支持全局字幕
     */
    private static void processImage(MediaResourceVo resource, FFmpegFrameRecorder recorder,
                                     Java2DFrameConverter converter, BufferedImage previousImage, GlobalSubtitleVO globalSubtitle) throws IOException {
        // 获取转场效果
        String transitionEffect = getTransitionEffect();
        log.info("开始处理图片：{}，使用转场效果：{}", resource.getUrl(), transitionEffect);

        File tempFile = downloadImage(resource.getUrl());
        log.info("图片下载完成：{}", tempFile.getAbsolutePath());
        try {
            BufferedImage image = ImageIO.read(tempFile);
            if (image == null) {
                log.error("无法读取图片: {}", resource.getUrl());
                throw new IOException("无法读取图片: " + resource.getUrl());
            }
            log.info("图片尺寸：{}x{}", image.getWidth(), image.getHeight());
            BufferedImage resizedImage = resizeImage(image, VIDEO_WIDTH, VIDEO_HEIGHT);

            // 确定图片持续时间
            int duration = DEFAULT_IMAGE_DURATION;
            if (resource.getDuration() != null && resource.getDuration() > 0) {
                duration = resource.getDuration();
            }
            log.info("图片持续时间：{}秒", duration);

            // 添加全局字幕（顶部）和单独字幕（底部）
            int framesToWrite = FRAME_RATE * duration;
            log.info("将写入{}帧", framesToWrite);

            for (int i = 0; i < framesToWrite; i++) {
                try {
                    BufferedImage frameImg = deepCopy(resizedImage);

                    // 添加全局字幕
                    if (globalSubtitle != null) {
                        addGlobalSubtitle(frameImg, globalSubtitle, i);
                    }

                    // 添加单独字幕
                    if (resource.getSubtitle() != null && !resource.getSubtitle().isEmpty()) {
                        addSubtitle(frameImg, resource.getSubtitle());
                    }

                    // 转换并记录帧
                    org.bytedeco.javacv.Frame frame = converter.convert(frameImg);
                    if (frame != null) {
                        recorder.record(frame);
                    } else {
                        log.warn("第{}帧转换失败", i);
                    }

                    // 每100帧记录一次进度
                    if (i > 0 && i % 100 == 0) {
                        log.info("已处理{}帧，进度：{:.2f}%", i, (i * 100.0 / framesToWrite));
                    }
                } catch (Exception e) {
                    log.error("处理第{}帧时出错: {}", i, e.getMessage(), e);
                }
            }

            log.info("图片处理完成，共写入{}帧", framesToWrite);
        } catch (Exception e) {
            log.error("处理图片资源失败: {}", e.getMessage(), e);
            throw new IOException("处理图片资源失败: " + e.getMessage(), e);
        } finally {
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 处理视频资源，支持全局字幕
     */
    private static void processVideo(MediaResourceVo resource, FFmpegFrameRecorder recorder,
                                     Java2DFrameConverter converter, GlobalSubtitleVO globalSubtitle) throws IOException {
        log.info("开始处理视频：{}", resource.getUrl());
        File tempFile = downloadImage(resource.getUrl());
        log.info("视频下载完成：{}", tempFile.getAbsolutePath());

        try {
            try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(tempFile)) {
                grabber.start();
                log.info("视频信息：宽度={}px, 高度={}px, 帧率={}fps, 时长={}秒",
                        grabber.getImageWidth(),
                        grabber.getImageHeight(),
                        grabber.getFrameRate(),
                        grabber.getLengthInTime() / 1000000.0);

                Frame frame;
                int frameIdx = 0;
                int totalFrames = (int) (grabber.getLengthInTime() / 1000000.0 * grabber.getFrameRate());
                log.info("预计总帧数：{}", totalFrames);

                while ((frame = grabber.grab()) != null) {
                    try {
                        // 只处理视频帧，忽略音频帧
                        if (frame.image != null) {
                            BufferedImage image = converter.convert(frame);
                            if (image != null) {
                                // 调整视频帧大小以匹配目标尺寸
                                if (image.getWidth() != VIDEO_WIDTH || image.getHeight() != VIDEO_HEIGHT) {
                                    image = resizeImage(image, VIDEO_WIDTH, VIDEO_HEIGHT);
                                }

                                // 添加全局字幕
                                if (globalSubtitle != null) {
                                    addGlobalSubtitle(image, globalSubtitle, frameIdx);
                                }

                                // 添加单独字幕
                                if (resource.getSubtitle() != null && !resource.getSubtitle().isEmpty()) {
                                    addSubtitle(image, resource.getSubtitle());
                                }

                                // 转换回Frame并记录（只记录视频帧）
                                frame = converter.convert(image);
                                if (frame != null) {
                                    recorder.record(frame);
                                }

                                frameIdx++;
                            }
                        }
                        // 忽略音频帧，不进行任何处理

                        // 每100帧记录一次进度（只对视频帧计数）
                        if (frameIdx > 0 && frameIdx % 100 == 0) {
                            log.info("已处理{}帧，进度：{:.2f}%", frameIdx,
                                    totalFrames > 0 ? (frameIdx * 100.0 / totalFrames) : 0);
                        }
                    } catch (Exception e) {
                        log.error("处理第{}帧时出错: {}", frameIdx, e.getMessage());
                    }
                }

                log.info("视频处理完成，共处理{}帧", frameIdx);
                grabber.stop();
            }
        } catch (Exception e) {
            log.error("处理视频资源失败: {}", e.getMessage(), e);
            throw new IOException("处理视频资源失败: " + e.getMessage(), e);
        } finally {
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 调整图片大小和增强图片质量
     */
    private static BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) {
        // 计算保持宽高比的尺寸
        double originalRatio = (double) originalImage.getWidth() / originalImage.getHeight();
        double targetRatio = (double) targetWidth / targetHeight;

        int newWidth, newHeight;
        if (originalRatio > targetRatio) {
            newHeight = targetHeight;
            newWidth = (int) (newHeight * originalRatio);
        } else {
            newWidth = targetWidth;
            newHeight = (int) (newWidth / originalRatio);
        }

        // 创建新的图片，使用原始图片的类型
        BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, originalImage.getType());
        Graphics2D g = resizedImage.createGraphics();

        // 设置渲染提示，提高图片质量
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 设置背景为透明
        g.setComposite(AlphaComposite.Clear);
        g.fillRect(0, 0, targetWidth, targetHeight);
        g.setComposite(AlphaComposite.SrcOver);

        // 计算居中位置
        int x = (targetWidth - newWidth) / 2;
        int y = (targetHeight - newHeight) / 2;

        // 绘制调整后的图片
        g.drawImage(originalImage, x, y, newWidth, newHeight, null);
        g.dispose();

        return resizedImage;
    }

    /**
     * 增强图片亮度和对比度
     */
    private static BufferedImage enhanceImage(BufferedImage image) {
        BufferedImage enhancedImage = new BufferedImage(image.getWidth(), image.getHeight(), image.getType());

        // 创建亮度调整操作
        RescaleOp brightenOp = new RescaleOp(1.2f, 15.0f, null);  // 增加20%亮度和15的偏移
        brightenOp.filter(image, enhancedImage);

        return enhancedImage;
    }

    /**
     * 添加字幕，支持自动换行
     */
    private static void addSubtitle(BufferedImage image, String text) {
        Graphics2D g = image.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        // 获取文字样式
        TextStyle style = getTextStyle();

        // 设置字体
        Font font = new Font(style.fontName, style.fontStyle, SUBTITLE_FONT_SIZE);
        g.setFont(font);

        // 计算最大行宽
        int maxLineWidth = VIDEO_WIDTH - 2 * CONTENT_MARGIN;

        // 分割文本为多行
        java.util.List<String> lines = splitTextByPixelWidth(text, font, maxLineWidth, g);

        // 计算字幕位置
        FontMetrics metrics = g.getFontMetrics(font);
        int lineHeight = metrics.getHeight();
        int totalHeight = lineHeight * lines.size();
        int y = VIDEO_HEIGHT - 100; // 距离底部100像素

        // 计算背景区域
        int bgWidth = maxLineWidth + 40; // 左右各留20像素的边距
        int bgHeight = totalHeight + 20; // 上下各留10像素的边距
        int bgX = (VIDEO_WIDTH - bgWidth) / 2;
        int bgY = y - totalHeight;

        // 绘制字幕背景
        g.setColor(SUBTITLE_BG_COLOR);
        g.fillRect(bgX, bgY, bgWidth, bgHeight);

        // 逐行绘制文本
        int currentY = y - totalHeight + lineHeight;
        for (String line : lines) {
            int lineWidth = metrics.stringWidth(line);
            int x = (VIDEO_WIDTH - lineWidth) / 2;

            // 绘制文字描边
            g.setColor(style.outlineColor);
            g.setStroke(new BasicStroke(style.outlineWidth));
            Shape outline = new TextLayout(line, font, g.getFontRenderContext()).getOutline(null);
            AffineTransform t = AffineTransform.getTranslateInstance(x, currentY);
            g.draw(t.createTransformedShape(outline));

            // 绘制字幕文本
            g.setColor(style.textColor);
            g.drawString(line, x, currentY);

            currentY += lineHeight;
        }

        g.dispose();
    }

    /**
     * 添加全局字幕（顶部）
     *
     * @param image      图片
     * @param subtitle   字幕对象
     * @param frameIndex 当前帧索引（用于动画，静态时传-1）
     */
    private static void addGlobalSubtitle(BufferedImage image, GlobalSubtitleVO subtitle, int frameIndex) {
        Graphics2D g = image.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        // 获取文字样式
        TextStyle style = getTextStyle();

        // 主标题动画参数
        float scale = 1.0f;
        float alpha = 1.0f;
        if (frameIndex >= 0 && frameIndex < ANIM_FRAMES) {
            scale = 0.7f + 0.02f * frameIndex; // 0.7~1.0
            alpha = 0.5f + 0.033f * frameIndex; // 0.5~1.0
        }

        // 主标题绘制
        Font titleFont = new Font(style.fontName, style.fontStyle, TITLE_FONT_SIZE);
        g.setFont(titleFont);

        // 计算最大行宽
        int maxTitleWidth = VIDEO_WIDTH - 80; // 左右各留40像素的边距

        // 分割标题文本为多行
        java.util.List<String> titleLines = splitTextByPixelWidth(subtitle.getTitle(), titleFont, maxTitleWidth, g);

        FontMetrics titleMetrics = g.getFontMetrics(titleFont);
        int titleLineHeight = titleMetrics.getHeight();
        int totalTitleHeight = titleLineHeight * titleLines.size();
        int centerX = VIDEO_WIDTH / 2;
        int titleY = titleLineHeight + 40;

        // 绘制标题的每一行
        int currentTitleY = titleY;

        for (int i = 0; i < titleLines.size(); i++) {
            String line = titleLines.get(i);
            int lineWidth = titleMetrics.stringWidth(line);

            // 动画变换，始终以中心点为基准
            AffineTransform old = g.getTransform();
            Composite oldComp = g.getComposite();
            g.translate(centerX, currentTitleY);
            g.scale(scale, scale);
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha));

            // 阴影
            g.setColor(new Color(80, 80, 80, 180));
            g.drawString(line, -lineWidth / 2 + 4, 4);

            // 描边
            g.setColor(style.outlineColor);
            g.setStroke(new BasicStroke(style.outlineWidth * 4)); // 主标题描边更粗
            Shape outline = new TextLayout(line, titleFont, g.getFontRenderContext()).getOutline(null);
            AffineTransform t = AffineTransform.getTranslateInstance(-lineWidth / 2, 0);
            g.draw(t.createTransformedShape(outline));

            // 填充
            g.setColor(style.textColor);
            g.drawString(line, -lineWidth / 2, 0);

            g.setTransform(old);
            g.setComposite(oldComp);

            currentTitleY += titleLineHeight;
        }

        // 两侧装饰点 - 只在第一行标题两侧添加
        if (!titleLines.isEmpty()) {
            String firstLine = titleLines.get(0);
            int firstLineWidth = titleMetrics.stringWidth(firstLine);
            int decoY = titleY - (int) (titleLineHeight * scale * 0.5);
            int decoOffset = (int) (firstLineWidth * scale / 2) + 40;
            g.setColor(style.outlineColor);
            g.fillOval(centerX - decoOffset - 10, decoY, 18, 18);
            g.fillOval(centerX + decoOffset - 8, decoY, 18, 18);
        }

        // 内容多行描边+填充，自动按像素宽度换行
        if (subtitle.getContent() != null && !subtitle.getContent().isEmpty()) {
            // 为内容文字使用相同的样式
            TextStyle contentStyle = getTextStyle();
            Font contentFont = new Font(contentStyle.fontName, contentStyle.fontStyle, SUBTITLE_FONT_SIZE);
            g.setFont(contentFont);
            FontMetrics contentMetrics = g.getFontMetrics(contentFont);
            int maxLineWidth = VIDEO_WIDTH - 2 * CONTENT_MARGIN;
            java.util.List<String> contentLines = splitTextByPixelWidth(subtitle.getContent(), contentFont, maxLineWidth, g);
            int lineHeight = contentMetrics.getHeight();

            // 内容Y坐标从最后一行标题下方开始
            int contentY = currentTitleY + lineHeight;

            for (String line : contentLines) {
                int lineWidth = contentMetrics.stringWidth(line);
                int lineX = (VIDEO_WIDTH - lineWidth) / 2;
                // 描边
                g.setColor(contentStyle.outlineColor);
                g.setStroke(new BasicStroke(contentStyle.outlineWidth));
                Shape outline2 = new TextLayout(line, contentFont, g.getFontRenderContext()).getOutline(null);
                AffineTransform t2 = AffineTransform.getTranslateInstance(lineX, contentY);
                g.draw(t2.createTransformedShape(outline2));
                // 填充
                g.setColor(contentStyle.textColor);
                g.drawString(line, lineX, contentY);
                contentY += lineHeight;
            }
        }
        g.dispose();
    }

    /**
     * 按像素宽度自动分割文本为多行
     */
    private static java.util.List<String> splitTextByPixelWidth(String text, Font font, int maxWidth, Graphics2D g) {
        java.util.List<String> lines = new ArrayList<>();
        StringBuilder line = new StringBuilder();
        for (char c : text.toCharArray()) {
            line.append(c);
            int width = g.getFontMetrics(font).stringWidth(line.toString());
            if (width > maxWidth) {
                if (line.length() > 1) {
                    line.deleteCharAt(line.length() - 1);
                    lines.add(line.toString());
                    line = new StringBuilder();
                    line.append(c);
                }
            }
        }
        if (line.length() > 0) lines.add(line.toString());
        return lines;
    }

    /**
     * 生成临时文件路径
     */
    public static String generateTempVideoPath() {
        return System.getProperty("java.io.tmpdir") + File.separator +
                "pet_grooming_video_" + System.currentTimeMillis() + ".mp4";
    }


    // 深拷贝图片
    private static BufferedImage deepCopy(BufferedImage bi) {
        ColorModel cm = bi.getColorModel();
        boolean isAlphaPremultiplied = cm.isAlphaPremultiplied();
        WritableRaster raster = bi.copyData(null);
        return new BufferedImage(cm, raster, isAlphaPremultiplied, null);
    }

}
