package com.ruoyi.common.utils;

import com.ruoyi.common.vo.GlobalSubtitleVO;
import com.ruoyi.common.vo.MediaResourceVo;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.ffmpeg.global.avutil;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.font.TextLayout;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.awt.image.ColorModel;
import java.awt.image.RescaleOp;
import java.awt.image.WritableRaster;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

@Slf4j
public class VideoGenerateUtils {


    // 视频帧率
    private static final int FRAME_RATE = 30;
    // 默认每张图片显示时间（秒）
    private static final int DEFAULT_IMAGE_DURATION = 3;
    // 转场持续时间（秒）
    private static final double TRANSITION_DURATION = 0.5;
    // 视频比特率
    private static final int VIDEO_BITRATE = 2500000;  // 增加到2.5Mbps提高清晰度
    // 视频宽度
    private static final int VIDEO_WIDTH = 720;
    // 视频高度
    private static final int VIDEO_HEIGHT = 1280;
    // 视频格式
    private static final String VIDEO_FORMAT = "mp4";
    // 音频采样率
    private static final int SAMPLE_RATE = 44100;
    // 音频通道数
    private static final int AUDIO_CHANNELS = 2;
    // 字幕字体大小
    private static final int SUBTITLE_FONT_SIZE = 38; // 内容字体更大
    // 主标题字体大小
    private static final int TITLE_FONT_SIZE = 60;    // 主标题更大
    // 字幕颜色
    private static final Color SUBTITLE_COLOR = Color.WHITE;
    // 主标题颜色
    private static final Color TITLE_COLOR = new Color(255, 140, 0); // 橙色描边
    // 字幕背景颜色
    private static final Color SUBTITLE_BG_COLOR = new Color(0, 0, 0, 128);
    // 每行最大字符数
    private static final int MAX_CHARS_PER_LINE = 20;
    private static final int ANIM_FRAMES = 15;
    private static final int CONTENT_MARGIN = 60; // 内容左右边距

    // 随机数生成器
    private static final Random RANDOM = new Random();

    // 转场效果类型
    private static final String[] TRANSITION_EFFECTS = {
            "fade",                  // 淡入淡出
            "wipeleft",              // 从左擦除
            "wiperight",             // 从右擦除
            "wipeup",                // 从上擦除
            "wipedown",              // 从下擦除
            "slideleft",             // 从左滑动
            "slideright",            // 从右滑动
            "slideup",               // 从上滑动
            "slidedown",             // 从下滑动
            "circlecrop",            // 圆形裁剪
            "rectcrop",              // 矩形裁剪
            "distance",              // 距离变换
            "fadeblack",             // 黑色淡入淡出
            "fadewhite",             // 白色淡入淡出
            "radial",                // 径向擦除
            "smoothleft",            // 平滑左移
            "smoothright",           // 平滑右移
            "smoothup",              // 平滑上移
            "smoothdown",            // 平滑下移
            "circleopen",            // 圆形打开
            "circleclose",           // 圆形关闭
            "vertopen",              // 垂直打开
            "vertclose",             // 垂直关闭
            "horzopen",              // 水平打开
            "horzclose",             // 水平关闭
            "dissolve",              // 溶解
            "pixelize",              // 像素化
            "radial",                // 径向
            "squeezeh",              // 水平挤压
            "squeezev"               // 垂直挤压
    };

    // 文字样式类型
    private static final TextStyle[] TEXT_STYLES = {
            new TextStyle("微软雅黑", Font.BOLD, Color.WHITE, new Color(0, 0, 0, 180), 2.0f),
            new TextStyle("黑体", Font.BOLD, Color.WHITE, new Color(0, 0, 0, 200), 2.5f),
            new TextStyle("宋体", Font.BOLD, Color.WHITE, new Color(50, 50, 50, 180), 2.0f),
            new TextStyle("楷体", Font.BOLD, Color.WHITE, new Color(30, 30, 30, 180), 2.2f),
            new TextStyle("微软雅黑", Font.BOLD, new Color(255, 255, 200), new Color(80, 40, 0, 180), 2.0f),
            new TextStyle("微软雅黑", Font.BOLD, new Color(255, 240, 200), new Color(100, 50, 0, 180), 2.0f),
            new TextStyle("微软雅黑", Font.BOLD, new Color(220, 255, 220), new Color(0, 80, 0, 180), 2.0f),
            new TextStyle("微软雅黑", Font.BOLD, new Color(220, 220, 255), new Color(0, 0, 100, 180), 2.0f),
            new TextStyle("微软雅黑", Font.BOLD, new Color(255, 220, 220), new Color(100, 0, 0, 180), 2.0f),
            new TextStyle("黑体", Font.BOLD, new Color(255, 255, 180), new Color(120, 60, 0, 180), 2.5f),
            new TextStyle("黑体", Font.BOLD, new Color(180, 255, 180), new Color(0, 100, 0, 180), 2.5f),
            new TextStyle("黑体", Font.BOLD, new Color(180, 180, 255), new Color(0, 0, 120, 180), 2.5f),
            new TextStyle("宋体", Font.BOLD, new Color(255, 220, 180), new Color(100, 50, 0, 180), 2.0f),
            new TextStyle("宋体", Font.BOLD, new Color(220, 255, 180), new Color(0, 100, 0, 180), 2.0f),
            new TextStyle("宋体", Font.BOLD, new Color(220, 180, 255), new Color(50, 0, 100, 180), 2.0f),
            new TextStyle("楷体", Font.BOLD, new Color(255, 200, 200), new Color(100, 0, 0, 180), 2.2f),
            new TextStyle("楷体", Font.BOLD, new Color(200, 255, 200), new Color(0, 100, 0, 180), 2.2f),
            new TextStyle("楷体", Font.BOLD, new Color(200, 200, 255), new Color(0, 0, 100, 180), 2.2f),
            new TextStyle("微软雅黑", Font.BOLD, new Color(255, 255, 255), new Color(0, 0, 0, 200), 3.0f),
            new TextStyle("微软雅黑", Font.BOLD, new Color(255, 220, 100), new Color(100, 50, 0, 200), 2.5f)
    };

    // 文字样式类
    private static class TextStyle {
        String fontName;
        int fontStyle;
        Color textColor;
        Color outlineColor;
        float outlineWidth;

        public TextStyle(String fontName, int fontStyle, Color textColor, Color outlineColor, float outlineWidth) {
            this.fontName = fontName;
            this.fontStyle = fontStyle;
            this.textColor = textColor;
            this.outlineColor = outlineColor;
            this.outlineWidth = outlineWidth;
        }
    }

    static {
        // 设置FFmpeg日志回调
        avutil.av_log_set_level(avutil.AV_LOG_INFO);
    }


    /**
     * 将媒体资源列表转换为视频，支持全局字幕
     * 主视频播放背景音乐，结尾视频播放其自带的结尾音乐
     *
     * @param mediaResources 媒体资源列表
     * @param outputPath     输出视频路径
     * @param globalSubtitle 全局字幕对象（可为null）
     * @param audioPath      背景音频路径（可为null，仅用于主视频部分）
     * @return 生成的视频文件
     */
    public static File generateVideo(java.util.List<MediaResourceVo> mediaResources, String outputPath,
                                     GlobalSubtitleVO globalSubtitle, String audioPath) throws Exception {
        // 重置效果，确保每次生成新视频时使用新的随机效果
        resetEffects();

        // 1. 生成主视频
        File mainVideo = generateVideoFromImages(mediaResources, outputPath, globalSubtitle, audioPath);

        // 2. 获取结尾视频路径
        URL resource = VideoGenerateUtils.class.getClassLoader().getResource("templates/宠孩生活结尾.mp4");
        if (resource == null) {
            throw new FileNotFoundException("结尾视频文件未找到！");
        }
        String endVideoPath = URLDecoder.decode(resource.getPath(), "UTF-8").replaceFirst("^/", "");

        // 3. 拼接结尾视频（结尾视频保持自带音频）
        return concatEndVideoWithSeparateAudio(mainVideo.getAbsolutePath(), endVideoPath, outputPath, audioPath);
    }

    public static File generateVideoFromImages(List<MediaResourceVo> mediaResources, String outputPath,
                                               GlobalSubtitleVO globalSubtitle, String audioPath) throws IOException {
        if (mediaResources == null || mediaResources.isEmpty()) {
            throw new IllegalArgumentException("媒体资源列表不能为空");
        }

        log.info("开始生成视频（无音频），输出路径：{}", outputPath);
        log.info("媒体资源数量：{}", mediaResources.size());

        List<MediaResourceVo> visualResources = mediaResources.stream()
                .filter(r -> r.getType() == 1 || r.getType() == 2)
                .sorted(Comparator.comparing(MediaResourceVo::getOrder, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());

        if (visualResources.isEmpty()) {
            throw new IllegalArgumentException("至少需要一个图片或视频资源");
        }

        log.info("视觉资源数量：{}", visualResources.size());

        String tempVideoPath = generateTempVideoPath();
        File tempVideoFile = new File(tempVideoPath);
        File outputFile = new File(outputPath);

        FFmpegFrameRecorder recorder = null;
        Java2DFrameConverter converter = new Java2DFrameConverter();
        BufferedImage previousImage = null;

        try {
            recorder = new FFmpegFrameRecorder(tempVideoFile, VIDEO_WIDTH, VIDEO_HEIGHT);
            recorder.setVideoCodec(avcodec.AV_CODEC_ID_MPEG4);
            recorder.setFormat(VIDEO_FORMAT);
            recorder.setFrameRate(FRAME_RATE);
            recorder.setVideoBitrate(VIDEO_BITRATE);
            recorder.setAudioChannels(AUDIO_CHANNELS); // 可保留设置，防止报错
            recorder.setSampleRate(SAMPLE_RATE);

            recorder.setVideoOption("preset", "ultrafast");
            recorder.setVideoOption("crf", "28");
            recorder.setVideoOption("vsync", "1");

            recorder.setAudioCodec(avcodec.AV_CODEC_ID_AAC); // 保留编码器配置以兼容后续音频添加
            recorder.setAudioOption("b:a", "192k");

            recorder.setPixelFormat(avutil.AV_PIX_FMT_YUV420P);
            recorder.setGopSize(FRAME_RATE * 2);

            log.info("初始化视频录制器...");
            recorder.start();
            log.info("录制器初始化完成");

            for (MediaResourceVo resource : visualResources) {
                log.info("处理资源：type={}, url={}, order={}, subtitle={}",
                        resource.getType(), resource.getUrl(), resource.getOrder(), resource.getSubtitle());

                switch (resource.getType()) {
                    case 1: // 图片
                        File tempFile = downloadImage(resource.getUrl());
                        try {
                            BufferedImage currentImage = ImageIO.read(tempFile);
                            if (currentImage != null) {
                                currentImage = resizeImage(currentImage, VIDEO_WIDTH, VIDEO_HEIGHT);
                                processImage(resource, recorder, converter, previousImage, globalSubtitle);
                                previousImage = currentImage;
                            }
                        } finally {
                            if (tempFile != null && tempFile.exists()) {
                                tempFile.delete();
                            }
                        }
                        break;
                    case 2: // 视频
                        processVideo(resource, recorder, converter, globalSubtitle);
                        break;
                }
            }

            log.info("视觉资源处理完成");
            recorder.stop();
            recorder.release();
            log.info("视频录制完成");

            if (outputFile.exists()) {
                outputFile.delete();
            }
            if (!tempVideoFile.renameTo(outputFile)) {
                throw new IOException("重命名视频文件失败");
            }

            if (!outputFile.exists() || outputFile.length() == 0) {
                throw new IOException("生成的视频文件无效");
            }

            log.info("视频生成成功（无音频），大小：{}MB", outputFile.length() / 1024 / 1024);
            return outputFile;

        } catch (Exception e) {
            log.error("生成视频失败", e);
            throw new IOException("生成视频失败: " + e.getMessage());
        } finally {
            if (recorder != null) {
                try {
                    recorder.stop();
                    recorder.release();
                } catch (Exception e) {
                    log.error("关闭视频录制器失败", e);
                }
            }

            if (tempVideoFile.exists()) {
                tempVideoFile.delete();
            }

            log.info("音频路径: {}, 文件是否存在: {}", audioPath, audioPath != null && new File(audioPath).exists());
        }
    }


    public static File concatEndVideo(String mainVideoPath, String endVideoPath, String outputPath, String audioPath) throws IOException {
        return concatEndVideoWithSeparateAudio(mainVideoPath, endVideoPath, outputPath, audioPath);
    }

    public static File concatEndVideoWithSeparateAudio(String mainVideoPath, String endVideoPath, String outputPath, String audioPath) throws IOException {
        log.info("开始拼接结尾视频，主视频：{}，结尾视频：{}，输出路径：{}，背景音乐：{}",
                mainVideoPath, endVideoPath, outputPath, audioPath);

        File mainVideoFile = new File(mainVideoPath);
        File endVideoFile = new File(endVideoPath);
        if (!mainVideoFile.exists() || mainVideoFile.length() == 0) {
            throw new IOException("主视频文件无效");
        }
        if (!endVideoFile.exists() || endVideoFile.length() == 0) {
            throw new IOException("结尾视频文件无效");
        }

        // 获取主视频和结尾视频的时长
        double mainVideoDuration = getVideoDuration(mainVideoPath);
        double endVideoDuration = getVideoDuration(endVideoPath);
        log.info("主视频时长：{} 秒，结尾视频时长：{} 秒", mainVideoDuration, endVideoDuration);

        // 1. 为主视频添加背景音乐（如果提供）
        String mainVideoWithAudio = mainVideoPath;
        if (audioPath != null && new File(audioPath).exists()) {
            mainVideoWithAudio = mainVideoPath.replace(".mp4", "_with_bg_audio.mp4");

            // 循环背景音乐以匹配主视频时长
            String paddedBgAudio = mainVideoPath.replace(".mp4", "_padded_bg_audio.m4a");
            log.info("为主视频循环背景音乐，时长：{} 秒", mainVideoDuration);
            runFFmpeg(String.format(
                    "ffmpeg -y -stream_loop -1 -i \"%s\" -t %.2f -c:a aac -b:a 128k \"%s\"",
                    audioPath, mainVideoDuration, paddedBgAudio
            ));

            // 将背景音乐添加到主视频
            runFFmpeg(String.format(
                    "ffmpeg -y -i \"%s\" -i \"%s\" -c:v copy -map 0:v:0 -map 1:a:0 -shortest -c:a aac -b:a 128k \"%s\"",
                    mainVideoPath, paddedBgAudio, mainVideoWithAudio
            ));

            // 清理临时文件
            new File(paddedBgAudio).delete();
        }

        // 2. 结尾视频保持原有音频（宠孩生活结尾.mp4 本身包含结尾音乐）
        // 不需要额外处理，直接使用原始的结尾视频文件
        String endVideoWithAudio = endVideoPath;
        log.info("结尾视频保持原有音频轨道");

        // 3. 标准化视频格式
        String normMain = mainVideoWithAudio.replace(".mp4", "_norm.mp4");
        runFFmpeg(String.format(
                "ffmpeg -y -i \"%s\" -vf scale=720:1280,fps=30 -c:v libx264 -crf 18 -preset fast -c:a aac -b:a 128k \"%s\"",
                mainVideoWithAudio, normMain
        ));

        String normEnd = endVideoWithAudio.replace(".mp4", "_norm.mp4");
        runFFmpeg(String.format(
                "ffmpeg -y -i \"%s\" -vf scale=720:1280,fps=30 -c:v libx264 -crf 18 -preset fast -c:a aac -b:a 128k \"%s\"",
                endVideoWithAudio, normEnd
        ));

        // 4. 创建 concat list 文件
        String listPath = System.getProperty("java.io.tmpdir") + "/concat_list_" + System.currentTimeMillis() + ".txt";
        try (PrintWriter pw = new PrintWriter(new FileWriter(listPath))) {
            pw.println("file '" + normMain.replace("\\", "\\\\") + "'");
            pw.println("file '" + normEnd.replace("\\", "\\\\") + "'");
        }

        // 5. 拼接两个视频
        String finalPath = outputPath;
        runFFmpeg(String.format(
                "ffmpeg -y -f concat -safe 0 -i \"%s\" -c copy \"%s\"",
                listPath, finalPath
        ));

        // 6. 校验输出文件
        File outputFile = new File(finalPath);
        if (!outputFile.exists() || outputFile.length() == 0) {
            throw new IOException("拼接输出失败：" + finalPath);
        }

        // 7. 清理中间文件
        new File(normMain).delete();
        new File(normEnd).delete();
        new File(listPath).delete();

        // 清理带音频的临时文件（只清理主视频的临时文件，结尾视频使用原文件）
        if (!mainVideoWithAudio.equals(mainVideoPath)) {
            new File(mainVideoWithAudio).delete();
        }

        log.info("拼接完成：{}", outputFile.getAbsolutePath());
        return outputFile;
    }


    public static double getVideoDuration(String videoPath) throws IOException {
        FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(videoPath);
        try {
            grabber.start();
            return grabber.getLengthInTime() / 1_000_000.0; // 转为秒
        } catch (Exception e) {
            throw new IOException("获取视频时长失败: " + e.getMessage(), e);
        } finally {
            try {
                grabber.stop();
            } catch (Exception ignored) {
            }
        }
    }

    private static void runFFmpeg(String command) throws IOException {
        log.info("执行FFmpeg命令：{}", command);
        Process process = Runtime.getRuntime().exec(command);
        try (BufferedReader err = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
            String line;
            while ((line = err.readLine()) != null) {
                log.debug(line);
            }
        }
        try {
            int exit = process.waitFor();
            if (exit != 0) throw new IOException("FFmpeg执行失败，退出码：" + exit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("FFmpeg命令中断", e);
        }
    }


    /**
     * 下载图片到临时文件
     */
    private static File downloadImage(String imageUrl) throws IOException {
        int maxRetries = 3;
        int retryCount = 0;
        int connectTimeout = 30000;  // 30秒连接超时
        int readTimeout = 30000;     // 30秒读取超时

        while (retryCount < maxRetries) {
            try {
                URL url = new URL(imageUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(connectTimeout);
                connection.setReadTimeout(readTimeout);
                connection.setRequestProperty("User-Agent", "Mozilla/5.0");
                connection.setRequestProperty("Accept", "image/*");

                log.info("开始下载图片，URL: {}, 重试次数: {}", imageUrl, retryCount);

                if (connection.getResponseCode() != HttpURLConnection.HTTP_OK) {
                    throw new IOException("下载图片失败，HTTP状态码: " + connection.getResponseCode());
                }

                String tempDir = System.getProperty("java.io.tmpdir");
                String fileName = "temp_image_" + System.currentTimeMillis() + ".jpg";
                File tempFile = new File(tempDir, fileName);

                try (InputStream in = new BufferedInputStream(connection.getInputStream());
                     FileOutputStream out = new FileOutputStream(tempFile)) {
                    byte[] buffer = new byte[8192];  // 增加缓冲区大小
                    int bytesRead;
                    long totalBytesRead = 0;
                    long startTime = System.currentTimeMillis();

                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                        totalBytesRead += bytesRead;

                        // 检查是否超时
                        if (System.currentTimeMillis() - startTime > readTimeout) {
                            throw new IOException("下载超时，已下载: " + totalBytesRead + " 字节");
                        }
                    }
                }

                log.info("图片下载完成，大小: {} 字节", tempFile.length());
                return tempFile;

            } catch (IOException e) {
                retryCount++;
                log.warn("下载图片失败，URL: {}, 重试次数: {}, 错误: {}", imageUrl, retryCount, e.getMessage());

                if (retryCount >= maxRetries) {
                    throw new IOException("下载图片失败，已重试" + maxRetries + "次: " + e.getMessage(), e);
                }

                // 重试前等待一段时间
                try {
                    Thread.sleep(1000 * retryCount);  // 递增等待时间
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new IOException("下载被中断", ie);
                }
            }
        }

        throw new IOException("下载图片失败，超过最大重试次数");
    }

    // 当前视频使用的转场效果和文字样式
    private static String currentTransitionEffect = null;
    private static TextStyle currentTextStyle = null;

    /**
     * 获取转场效果，每次生成视频只使用一种
     */
    private static String getTransitionEffect() {
        if (currentTransitionEffect == null) {
            currentTransitionEffect = TRANSITION_EFFECTS[RANDOM.nextInt(TRANSITION_EFFECTS.length)];
            log.info("本次视频使用转场效果：{}", currentTransitionEffect);
        }
        return currentTransitionEffect;
    }

    /**
     * 获取文字样式，每次生成视频只使用一种
     */
    private static TextStyle getTextStyle() {
        if (currentTextStyle == null) {
            currentTextStyle = TEXT_STYLES[RANDOM.nextInt(TEXT_STYLES.length)];
            log.info("本次视频使用文字样式：字体={}, 颜色={}", currentTextStyle.fontName, currentTextStyle.textColor);
        }
        return currentTextStyle;
    }

    /**
     * 重置当前使用的效果，在每次生成新视频前调用
     */
    private static void resetEffects() {
        currentTransitionEffect = null;
        currentTextStyle = null;
    }

    /**
     * 处理图片资源，支持全局字幕
     */
    private static void processImage(MediaResourceVo resource, FFmpegFrameRecorder recorder,
                                     Java2DFrameConverter converter, BufferedImage previousImage, GlobalSubtitleVO globalSubtitle) throws IOException {
        // 获取转场效果
        String transitionEffect = getTransitionEffect();
        log.info("开始处理图片：{}，使用转场效果：{}", resource.getUrl(), transitionEffect);

        File tempFile = downloadImage(resource.getUrl());
        log.info("图片下载完成：{}", tempFile.getAbsolutePath());
        try {
            BufferedImage image = ImageIO.read(tempFile);
            if (image == null) {
                log.error("无法读取图片: {}", resource.getUrl());
                throw new IOException("无法读取图片: " + resource.getUrl());
            }
            log.info("图片尺寸：{}x{}", image.getWidth(), image.getHeight());
            BufferedImage resizedImage = resizeImage(image, VIDEO_WIDTH, VIDEO_HEIGHT);

            // 确定图片持续时间
            int duration = DEFAULT_IMAGE_DURATION;
            if (resource.getDuration() != null && resource.getDuration() > 0) {
                duration = resource.getDuration();
            }
            log.info("图片持续时间：{}秒", duration);

            // 添加全局字幕（顶部）和单独字幕（底部）
            int framesToWrite = FRAME_RATE * duration;
            log.info("将写入{}帧", framesToWrite);

            for (int i = 0; i < framesToWrite; i++) {
                try {
                    BufferedImage frameImg = deepCopy(resizedImage);

                    // 添加全局字幕
                    if (globalSubtitle != null) {
                        addGlobalSubtitle(frameImg, globalSubtitle, i);
                    }

                    // 添加单独字幕
                    if (resource.getSubtitle() != null && !resource.getSubtitle().isEmpty()) {
                        addSubtitle(frameImg, resource.getSubtitle());
                    }

                    // 转换并记录帧
                    org.bytedeco.javacv.Frame frame = converter.convert(frameImg);
                    if (frame != null) {
                        recorder.record(frame);
                    } else {
                        log.warn("第{}帧转换失败", i);
                    }

                    // 每100帧记录一次进度
                    if (i > 0 && i % 100 == 0) {
                        log.info("已处理{}帧，进度：{:.2f}%", i, (i * 100.0 / framesToWrite));
                    }
                } catch (Exception e) {
                    log.error("处理第{}帧时出错: {}", i, e.getMessage(), e);
                }
            }

            log.info("图片处理完成，共写入{}帧", framesToWrite);
        } catch (Exception e) {
            log.error("处理图片资源失败: {}", e.getMessage(), e);
            throw new IOException("处理图片资源失败: " + e.getMessage(), e);
        } finally {
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 处理视频资源，支持全局字幕
     */
    private static void processVideo(MediaResourceVo resource, FFmpegFrameRecorder recorder,
                                     Java2DFrameConverter converter, GlobalSubtitleVO globalSubtitle) throws IOException {
        log.info("开始处理视频：{}", resource.getUrl());
        File tempFile = downloadImage(resource.getUrl());
        log.info("视频下载完成：{}", tempFile.getAbsolutePath());

        try {
            try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(tempFile)) {
                grabber.start();
                log.info("视频信息：宽度={}px, 高度={}px, 帧率={}fps, 时长={}秒",
                        grabber.getImageWidth(),
                        grabber.getImageHeight(),
                        grabber.getFrameRate(),
                        grabber.getLengthInTime() / 1000000.0);

                Frame frame;
                int frameIdx = 0;
                int totalFrames = (int) (grabber.getLengthInTime() / 1000000.0 * grabber.getFrameRate());
                log.info("预计总帧数：{}", totalFrames);

                while ((frame = grabber.grab()) != null) {
                    try {
                        if (frame.image != null) {
                            BufferedImage image = converter.convert(frame);
                            if (image != null) {
                                // 调整视频帧大小以匹配目标尺寸
                                if (image.getWidth() != VIDEO_WIDTH || image.getHeight() != VIDEO_HEIGHT) {
                                    image = resizeImage(image, VIDEO_WIDTH, VIDEO_HEIGHT);
                                }

                                // 添加全局字幕
                                if (globalSubtitle != null) {
                                    addGlobalSubtitle(image, globalSubtitle, frameIdx);
                                }

                                // 添加单独字幕
                                if (resource.getSubtitle() != null && !resource.getSubtitle().isEmpty()) {
                                    addSubtitle(image, resource.getSubtitle());
                                }

                                // 转换回Frame并记录
                                frame = converter.convert(image);
                            }
                        }

                        if (frame != null) {
                            recorder.record(frame);
                        }

                        frameIdx++;
                        // 每100帧记录一次进度
                        if (frameIdx > 0 && frameIdx % 100 == 0) {
                            log.info("已处理{}帧，进度：{:.2f}%", frameIdx,
                                    totalFrames > 0 ? (frameIdx * 100.0 / totalFrames) : 0);
                        }
                    } catch (Exception e) {
                        log.error("处理第{}帧时出错: {}", frameIdx, e.getMessage());
                    }
                }

                log.info("视频处理完成，共处理{}帧", frameIdx);
                grabber.stop();
            }
        } catch (Exception e) {
            log.error("处理视频资源失败: {}", e.getMessage(), e);
            throw new IOException("处理视频资源失败: " + e.getMessage(), e);
        } finally {
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 调整图片大小和增强图片质量
     */
    private static BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) {
        // 计算保持宽高比的尺寸
        double originalRatio = (double) originalImage.getWidth() / originalImage.getHeight();
        double targetRatio = (double) targetWidth / targetHeight;

        int newWidth, newHeight;
        if (originalRatio > targetRatio) {
            newHeight = targetHeight;
            newWidth = (int) (newHeight * originalRatio);
        } else {
            newWidth = targetWidth;
            newHeight = (int) (newWidth / originalRatio);
        }

        // 创建新的图片，使用原始图片的类型
        BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, originalImage.getType());
        Graphics2D g = resizedImage.createGraphics();

        // 设置渲染提示，提高图片质量
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 设置背景为透明
        g.setComposite(AlphaComposite.Clear);
        g.fillRect(0, 0, targetWidth, targetHeight);
        g.setComposite(AlphaComposite.SrcOver);

        // 计算居中位置
        int x = (targetWidth - newWidth) / 2;
        int y = (targetHeight - newHeight) / 2;

        // 绘制调整后的图片
        g.drawImage(originalImage, x, y, newWidth, newHeight, null);
        g.dispose();

        return resizedImage;
    }

    /**
     * 增强图片亮度和对比度
     */
    private static BufferedImage enhanceImage(BufferedImage image) {
        BufferedImage enhancedImage = new BufferedImage(image.getWidth(), image.getHeight(), image.getType());

        // 创建亮度调整操作
        RescaleOp brightenOp = new RescaleOp(1.2f, 15.0f, null);  // 增加20%亮度和15的偏移
        brightenOp.filter(image, enhancedImage);

        return enhancedImage;
    }

    /**
     * 添加字幕，支持自动换行
     */
    private static void addSubtitle(BufferedImage image, String text) {
        Graphics2D g = image.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        // 获取文字样式
        TextStyle style = getTextStyle();

        // 设置字体
        Font font = new Font(style.fontName, style.fontStyle, SUBTITLE_FONT_SIZE);
        g.setFont(font);

        // 计算最大行宽
        int maxLineWidth = VIDEO_WIDTH - 2 * CONTENT_MARGIN;

        // 分割文本为多行
        java.util.List<String> lines = splitTextByPixelWidth(text, font, maxLineWidth, g);

        // 计算字幕位置
        FontMetrics metrics = g.getFontMetrics(font);
        int lineHeight = metrics.getHeight();
        int totalHeight = lineHeight * lines.size();
        int y = VIDEO_HEIGHT - 100; // 距离底部100像素

        // 计算背景区域
        int bgWidth = maxLineWidth + 40; // 左右各留20像素的边距
        int bgHeight = totalHeight + 20; // 上下各留10像素的边距
        int bgX = (VIDEO_WIDTH - bgWidth) / 2;
        int bgY = y - totalHeight;

        // 绘制字幕背景
        g.setColor(SUBTITLE_BG_COLOR);
        g.fillRect(bgX, bgY, bgWidth, bgHeight);

        // 逐行绘制文本
        int currentY = y - totalHeight + lineHeight;
        for (String line : lines) {
            int lineWidth = metrics.stringWidth(line);
            int x = (VIDEO_WIDTH - lineWidth) / 2;

            // 绘制文字描边
            g.setColor(style.outlineColor);
            g.setStroke(new BasicStroke(style.outlineWidth));
            Shape outline = new TextLayout(line, font, g.getFontRenderContext()).getOutline(null);
            AffineTransform t = AffineTransform.getTranslateInstance(x, currentY);
            g.draw(t.createTransformedShape(outline));

            // 绘制字幕文本
            g.setColor(style.textColor);
            g.drawString(line, x, currentY);

            currentY += lineHeight;
        }

        g.dispose();
    }

    /**
     * 添加全局字幕（顶部）
     *
     * @param image      图片
     * @param subtitle   字幕对象
     * @param frameIndex 当前帧索引（用于动画，静态时传-1）
     */
    private static void addGlobalSubtitle(BufferedImage image, GlobalSubtitleVO subtitle, int frameIndex) {
        Graphics2D g = image.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        // 获取文字样式
        TextStyle style = getTextStyle();

        // 主标题动画参数
        float scale = 1.0f;
        float alpha = 1.0f;
        if (frameIndex >= 0 && frameIndex < ANIM_FRAMES) {
            scale = 0.7f + 0.02f * frameIndex; // 0.7~1.0
            alpha = 0.5f + 0.033f * frameIndex; // 0.5~1.0
        }

        // 主标题绘制
        Font titleFont = new Font(style.fontName, style.fontStyle, TITLE_FONT_SIZE);
        g.setFont(titleFont);

        // 计算最大行宽
        int maxTitleWidth = VIDEO_WIDTH - 80; // 左右各留40像素的边距

        // 分割标题文本为多行
        java.util.List<String> titleLines = splitTextByPixelWidth(subtitle.getTitle(), titleFont, maxTitleWidth, g);

        FontMetrics titleMetrics = g.getFontMetrics(titleFont);
        int titleLineHeight = titleMetrics.getHeight();
        int totalTitleHeight = titleLineHeight * titleLines.size();
        int centerX = VIDEO_WIDTH / 2;
        int titleY = titleLineHeight + 40;

        // 绘制标题的每一行
        int currentTitleY = titleY;

        for (int i = 0; i < titleLines.size(); i++) {
            String line = titleLines.get(i);
            int lineWidth = titleMetrics.stringWidth(line);

            // 动画变换，始终以中心点为基准
            AffineTransform old = g.getTransform();
            Composite oldComp = g.getComposite();
            g.translate(centerX, currentTitleY);
            g.scale(scale, scale);
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha));

            // 阴影
            g.setColor(new Color(80, 80, 80, 180));
            g.drawString(line, -lineWidth / 2 + 4, 4);

            // 描边
            g.setColor(style.outlineColor);
            g.setStroke(new BasicStroke(style.outlineWidth * 4)); // 主标题描边更粗
            Shape outline = new TextLayout(line, titleFont, g.getFontRenderContext()).getOutline(null);
            AffineTransform t = AffineTransform.getTranslateInstance(-lineWidth / 2, 0);
            g.draw(t.createTransformedShape(outline));

            // 填充
            g.setColor(style.textColor);
            g.drawString(line, -lineWidth / 2, 0);

            g.setTransform(old);
            g.setComposite(oldComp);

            currentTitleY += titleLineHeight;
        }

        // 两侧装饰点 - 只在第一行标题两侧添加
        if (!titleLines.isEmpty()) {
            String firstLine = titleLines.get(0);
            int firstLineWidth = titleMetrics.stringWidth(firstLine);
            int decoY = titleY - (int) (titleLineHeight * scale * 0.5);
            int decoOffset = (int) (firstLineWidth * scale / 2) + 40;
            g.setColor(style.outlineColor);
            g.fillOval(centerX - decoOffset - 10, decoY, 18, 18);
            g.fillOval(centerX + decoOffset - 8, decoY, 18, 18);
        }

        // 内容多行描边+填充，自动按像素宽度换行
        if (subtitle.getContent() != null && !subtitle.getContent().isEmpty()) {
            // 为内容文字使用相同的样式
            TextStyle contentStyle = getTextStyle();
            Font contentFont = new Font(contentStyle.fontName, contentStyle.fontStyle, SUBTITLE_FONT_SIZE);
            g.setFont(contentFont);
            FontMetrics contentMetrics = g.getFontMetrics(contentFont);
            int maxLineWidth = VIDEO_WIDTH - 2 * CONTENT_MARGIN;
            java.util.List<String> contentLines = splitTextByPixelWidth(subtitle.getContent(), contentFont, maxLineWidth, g);
            int lineHeight = contentMetrics.getHeight();

            // 内容Y坐标从最后一行标题下方开始
            int contentY = currentTitleY + lineHeight;

            for (String line : contentLines) {
                int lineWidth = contentMetrics.stringWidth(line);
                int lineX = (VIDEO_WIDTH - lineWidth) / 2;
                // 描边
                g.setColor(contentStyle.outlineColor);
                g.setStroke(new BasicStroke(contentStyle.outlineWidth));
                Shape outline2 = new TextLayout(line, contentFont, g.getFontRenderContext()).getOutline(null);
                AffineTransform t2 = AffineTransform.getTranslateInstance(lineX, contentY);
                g.draw(t2.createTransformedShape(outline2));
                // 填充
                g.setColor(contentStyle.textColor);
                g.drawString(line, lineX, contentY);
                contentY += lineHeight;
            }
        }
        g.dispose();
    }

    /**
     * 按像素宽度自动分割文本为多行
     */
    private static java.util.List<String> splitTextByPixelWidth(String text, Font font, int maxWidth, Graphics2D g) {
        java.util.List<String> lines = new ArrayList<>();
        StringBuilder line = new StringBuilder();
        for (char c : text.toCharArray()) {
            line.append(c);
            int width = g.getFontMetrics(font).stringWidth(line.toString());
            if (width > maxWidth) {
                if (line.length() > 1) {
                    line.deleteCharAt(line.length() - 1);
                    lines.add(line.toString());
                    line = new StringBuilder();
                    line.append(c);
                }
            }
        }
        if (line.length() > 0) lines.add(line.toString());
        return lines;
    }

    /**
     * 生成临时文件路径
     */
    public static String generateTempVideoPath() {
        return System.getProperty("java.io.tmpdir") + File.separator +
                "pet_grooming_video_" + System.currentTimeMillis() + ".mp4";
    }


    // 深拷贝图片
    private static BufferedImage deepCopy(BufferedImage bi) {
        ColorModel cm = bi.getColorModel();
        boolean isAlphaPremultiplied = cm.isAlphaPremultiplied();
        WritableRaster raster = bi.copyData(null);
        return new BufferedImage(cm, raster, isAlphaPremultiplied, null);
    }

}
