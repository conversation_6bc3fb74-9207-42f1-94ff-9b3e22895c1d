package com.ruoyi.common.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.vo.GlobalSubtitleVO;
import com.ruoyi.common.vo.MediaResourceVo;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

public class VideoGeneratorUtilTest {

    public static void main(String[] args) throws Exception {
        // 你的JSON字符串
        String json = "{\"code\":200,\"msg\":\"操作成功\",\"data\":{\"id\":\"1926563302701096960\",\"petName\":\"开心\",\"petType\":2,\"breed\":\"泰迪\",\"gender\":1,\"petId\":\"1926561886527905792\",\"serviceType\":1,\"serviceTime\":\"2025-05-25 16:57:38\",\"beforePhotos\":\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/4a623d7c850b49ed9f57bdb14d6b7809.jpg\",\"beforeDescription\":\"伊珊娜精致洗护\",\"afterPhotos\":\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/ff81883e74054393a2a7e9aebcb2b70a.jpg\",\"afterDescription\":\"\",\"notes\":\"\",\"isDeleted\":0,\"createUser\":\"1909185096332656640\",\"createTime\":\"2025-05-25 16:57:40\",\"modifiedUser\":null,\"modifiedTime\":null,\"fkOrgId\":\"1907724611263447040\",\"serviceUserId\":\"1909185096332656640\",\"appointmentId\":\"0\",\"canShare\":0,\"userId\":\"1915551480184619008\",\"contactNumber\":\"13924099528\",\"weight\":2.00,\"isShared\":0,\"shareImage\":null,\"shareTime\":null,\"shareVerifyUser\":null,\"serviceTimeStart\":null,\"serviceTimeEnd\":null,\"keyword\":null,\"orgId\":null,\"serviceUserName\":\"周胜男\",\"genderName\":\"公\",\"petTypeName\":\"狗狗\",\"serviceTypeName\":\"洗护服务\",\"processPhotoVos\":[{\"key\":\"\",\"name\":\"脚底毛修剪\",\"videoUrl\":null,\"beforeUrl\":\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/29bb4b584ca144a381e62dfd7e94ea1c.jpg\",\"afterUrl\":\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/e3432e85bf324d82b74902c5e8d6e355.jpg\"},{\"key\":\"\",\"name\":\"指甲修剪\",\"videoUrl\":null,\"beforeUrl\":\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/d0a9a2c535754f52b8f086307ac99e01.jpg\",\"afterUrl\":\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/a9fec78b4eab4be8ace2ecb387e2a114.jpg\"},{\"key\":\"\",\"name\":\"驱虫\",\"videoUrl\":\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/a47a0aba77714df2868a93fef9cc79a1.mp4\",\"beforeUrl\":\"\",\"afterUrl\":\"\"}],\"videoUrlList\":null,\"imageUrlList\":[\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/c7ed224284004871855d2b09738c90bb.jpg\"],\"shareVerifyUserName\":null,\"orgName\":null,\"title\":null,\"storeAddress\":null}}";

        JSONObject obj = JSON.parseObject(json).getJSONObject("data");
        List<MediaResourceVo> resources = new ArrayList<>();
        int order = 1;

        // 进店前
        String beforePhoto = obj.getString("beforePhotos");
        String beforeDesc = obj.getString("beforeDescription");
        if (beforePhoto != null && !beforePhoto.isEmpty()) {
            MediaResourceVo before = new MediaResourceVo();
            before.setType(1);
            before.setUrl(beforePhoto);
            before.setOrder(order++);
            before.setSubtitle("进店前 - " + (beforeDesc != null ? beforeDesc : ""));
            resources.add(before);
        }

        // 护理过程
        JSONArray processArr = obj.getJSONArray("processPhotoVos");
        if (processArr != null) {
            for (int i = 0; i < processArr.size(); i++) {
                JSONObject vo = processArr.getJSONObject(i);
                String name = vo.getString("name");
                String beforeUrl = vo.getString("beforeUrl");
                String afterUrl = vo.getString("afterUrl");
                String videoUrl = vo.getString("videoUrl");

                if (beforeUrl != null && !beforeUrl.isEmpty()) {
                    MediaResourceVo beforeStep = new MediaResourceVo();
                    beforeStep.setType(1);
                    beforeStep.setUrl(beforeUrl);
                    beforeStep.setOrder(order++);
                    beforeStep.setSubtitle(name + " - 前");
                    resources.add(beforeStep);
                }
                if (afterUrl != null && !afterUrl.isEmpty()) {
                    MediaResourceVo afterStep = new MediaResourceVo();
                    afterStep.setType(1);
                    afterStep.setUrl(afterUrl);
                    afterStep.setOrder(order++);
                    afterStep.setSubtitle(name + " - 后");
                    resources.add(afterStep);
                }
                if (videoUrl != null && !videoUrl.isEmpty()) {
                    MediaResourceVo videoStep = new MediaResourceVo();
                    videoStep.setType(2);
                    videoStep.setUrl(videoUrl);
                    videoStep.setOrder(order++);
                    videoStep.setSubtitle(name);
                    resources.add(videoStep);
                }
            }
        }

        // 护理后
        String afterPhoto = obj.getString("afterPhotos");
        String afterDesc = obj.getString("afterDescription");
        if (afterPhoto != null && !afterPhoto.isEmpty()) {
            MediaResourceVo after = new MediaResourceVo();
            after.setType(1);
            after.setUrl(afterPhoto);
            after.setOrder(order++);
            after.setSubtitle("护理后" + (afterDesc != null && !afterDesc.isEmpty() ? " - " + afterDesc : ""));
            resources.add(after);
        }

        // 全局字幕
        GlobalSubtitleVO globalSubtitle = new GlobalSubtitleVO();
        globalSubtitle.setTitle("贵阳宠物洗护新模式");
        globalSubtitle.setContent("洗护、美容、寄养消费都可以积分，积分可兑换，欢迎您的光临！");

        // 输出路径和音频
        String outputPath = "C:\\Users\\<USER>\\desktop\\pet_grooming_" + System.currentTimeMillis() + ".mp4";

        String audioPath = "C:\\Users\\<USER>\\desktop\\extracted_audio_1748591297575.aac";

        // 生成视频
        VideoGenerateUtils.generateVideo(resources, outputPath, globalSubtitle, audioPath);
        System.out.println("视频生成成功：" + outputPath);
    }
}



