package com.ruoyi.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "MediaResourceVo", description = "媒体资源")
public class MediaResourceVo {
    
    /**
     * 资源类型：1-图片，2-视频
     */
    @ApiModelProperty(value = "资源类型：1-图片，2-视频")
    private Integer type;
    
    /**
     * 资源URL
     */
    @ApiModelProperty(value = "资源URL")
    private String url;
    
    /**
     * 显示顺序
     */
    @ApiModelProperty(value = "显示顺序")
    private Integer order;
    
    /**
     * 字幕内容
     */
    @ApiModelProperty(value = "字幕内容")
    private String subtitle;
    
    /**
     * 显示时长（秒）
     */
    @ApiModelProperty(value = "显示时长（秒）")
    private Integer duration;
    
    @ApiModelProperty(value = "资源描述")
    private String description;
} 