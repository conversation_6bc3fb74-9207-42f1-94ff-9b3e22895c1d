#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MoviePy安装是否正确
"""

import sys

def test_moviepy_installation():
    """测试MoviePy各个模块的导入"""
    
    print("🔍 测试MoviePy安装...")
    
    # 测试基础导入
    try:
        import moviepy
        print(f"✅ moviepy 基础模块导入成功，版本：{moviepy.__version__}")
    except ImportError as e:
        print(f"❌ moviepy 基础模块导入失败：{e}")
        return False
    
    # 测试editor模块
    try:
        import moviepy.editor
        print("✅ moviepy.editor 模块导入成功")
    except ImportError as e:
        print(f"❌ moviepy.editor 模块导入失败：{e}")
        return False
    
    # 测试具体类
    try:
        from moviepy.editor import VideoFileClip
        print("✅ VideoFileClip 导入成功")
    except ImportError as e:
        print(f"❌ VideoFileClip 导入失败：{e}")
        return False
    
    try:
        from moviepy.editor import ImageClip
        print("✅ ImageClip 导入成功")
    except ImportError as e:
        print(f"❌ ImageClip 导入失败：{e}")
        return False
    
    try:
        from moviepy.editor import AudioFileClip
        print("✅ AudioFileClip 导入成功")
    except ImportError as e:
        print(f"❌ AudioFileClip 导入失败：{e}")
        return False
    
    try:
        from moviepy.editor import concatenate_videoclips
        print("✅ concatenate_videoclips 导入成功")
    except ImportError as e:
        print(f"❌ concatenate_videoclips 导入失败：{e}")
        return False
    
    try:
        from moviepy.editor import concatenate_audioclips
        print("✅ concatenate_audioclips 导入成功")
    except ImportError as e:
        print(f"❌ concatenate_audioclips 导入失败：{e}")
        return False
    
    print("\n🎉 MoviePy安装测试通过！")
    return True

def test_other_dependencies():
    """测试其他依赖"""
    
    print("\n🔍 测试其他依赖...")
    
    # 测试PIL
    try:
        from PIL import Image, ImageDraw, ImageFont
        print("✅ PIL/Pillow 导入成功")
    except ImportError as e:
        print(f"❌ PIL/Pillow 导入失败：{e}")
        return False
    
    # 测试numpy
    try:
        import numpy as np
        print(f"✅ NumPy 导入成功，版本：{np.__version__}")
    except ImportError as e:
        print(f"❌ NumPy 导入失败：{e}")
        return False
    
    # 测试requests
    try:
        import requests
        print(f"✅ Requests 导入成功，版本：{requests.__version__}")
    except ImportError as e:
        print(f"❌ Requests 导入失败：{e}")
        return False
    
    print("\n🎉 所有依赖测试通过！")
    return True

def show_installation_guide():
    """显示安装指南"""
    
    print("\n📦 安装指南：")
    print("=" * 50)
    
    print("\n1. 基础安装：")
    print("   pip install moviepy")
    print("   pip install Pillow")
    print("   pip install numpy")
    print("   pip install requests")
    
    print("\n2. 一次性安装：")
    print("   pip install moviepy Pillow numpy requests")
    
    print("\n3. 使用requirements.txt：")
    print("   pip install -r requirements.txt")
    
    print("\n4. 如果遇到问题：")
    print("   # 升级pip")
    print("   python -m pip install --upgrade pip")
    print("   ")
    print("   # 使用国内镜像")
    print("   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple moviepy")
    print("   ")
    print("   # 使用conda")
    print("   conda install -c conda-forge moviepy")
    
    print("\n5. 验证安装：")
    print("   python test_moviepy.py")

if __name__ == "__main__":
    print("🚀 MoviePy安装测试工具")
    print("=" * 50)
    
    # 显示Python版本
    print(f"Python版本：{sys.version}")
    print()
    
    # 测试MoviePy
    moviepy_ok = test_moviepy_installation()
    
    # 测试其他依赖
    deps_ok = test_other_dependencies()
    
    if moviepy_ok and deps_ok:
        print("\n🎉 所有测试通过！可以运行video_generator.py了")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，请按照以下指南安装依赖：")
        show_installation_guide()
        sys.exit(1)
