#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频生成工具 - Python版本
使用moviepy、PIL等库替代FFmpeg，提供更好的Python集成
"""

import os
import sys
import logging
import tempfile
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import requests
from PIL import Image, ImageDraw, ImageFont
import numpy as np
try:
    from moviepy.editor import (
        VideoFileClip, ImageClip, AudioFileClip, CompositeVideoClip,
        concatenate_videoclips, concatenate_audioclips, ColorClip
    )
except ImportError:
    print("❌ MoviePy未正确安装，请尝试以下命令：")
    print("pip install moviepy")
    print("或者：")
    print("conda install -c conda-forge moviepy")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class MediaResource:
    """媒体资源数据类"""
    type: int  # 1=图片, 2=视频
    order: int
    url: str
    duration: Optional[int] = None
    subtitle: Optional[str] = None

@dataclass
class GlobalSubtitle:
    """全局字幕数据类"""
    title: Optional[str] = None
    content: Optional[str] = None

class VideoGenerator:
    """视频生成器"""
    
    # 常量配置
    DEFAULT_IMAGE_DURATION = 8  # 默认图片显示时长（秒）
    VIDEO_SIZE = (720, 1280)    # 视频尺寸 (宽, 高)
    VIDEO_FPS = 30              # 视频帧率
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp(prefix="video_gen_")
        logger.info(f"临时目录创建：{self.temp_dir}")
    
    def __del__(self):
        """清理临时目录"""
        import shutil
        if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            logger.info(f"临时目录清理：{self.temp_dir}")
    
    def download_image(self, url: str, filename: str) -> str:
        """下载图片到临时目录"""
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            filepath = os.path.join(self.temp_dir, filename)
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            logger.info(f"图片下载完成：{filename}")
            return filepath
        except Exception as e:
            logger.error(f"图片下载失败 {url}: {e}")
            raise
    
    def add_subtitle_to_image(self, image_path: str, subtitle_text: str, 
                            global_subtitle: Optional[GlobalSubtitle] = None) -> str:
        """为图片添加字幕"""
        try:
            # 打开图片
            img = Image.open(image_path)
            img = img.resize(self.VIDEO_SIZE, Image.Resampling.LANCZOS)
            
            # 创建绘图对象
            draw = ImageDraw.Draw(img)
            
            # 尝试加载中文字体
            try:
                # Windows系统字体路径
                font_paths = [
                    "C:/Windows/Fonts/simhei.ttf",  # 黑体
                    "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
                    "/System/Library/Fonts/PingFang.ttc",  # macOS
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
                ]
                
                title_font = None
                content_font = None
                subtitle_font = None
                
                for font_path in font_paths:
                    if os.path.exists(font_path):
                        title_font = ImageFont.truetype(font_path, 48)
                        content_font = ImageFont.truetype(font_path, 32)
                        subtitle_font = ImageFont.truetype(font_path, 36)
                        break
                
                if not title_font:
                    # 使用默认字体
                    title_font = ImageFont.load_default()
                    content_font = ImageFont.load_default()
                    subtitle_font = ImageFont.load_default()
                    
            except Exception as e:
                logger.warning(f"字体加载失败，使用默认字体: {e}")
                title_font = ImageFont.load_default()
                content_font = ImageFont.load_default()
                subtitle_font = ImageFont.load_default()
            
            # 添加全局字幕
            if global_subtitle:
                y_offset = 80
                
                # 添加标题
                if global_subtitle.title:
                    self._draw_text_with_outline(
                        draw, global_subtitle.title, title_font,
                        self.VIDEO_SIZE[0] // 2, y_offset, 'white', 'black'
                    )
                    y_offset += 70
                
                # 添加内容
                if global_subtitle.content:
                    self._draw_text_with_outline(
                        draw, global_subtitle.content, content_font,
                        self.VIDEO_SIZE[0] // 2, y_offset, 'white', 'black'
                    )
            
            # 添加单独字幕（底部）
            if subtitle_text:
                self._draw_text_with_outline(
                    draw, subtitle_text, subtitle_font,
                    self.VIDEO_SIZE[0] // 2, self.VIDEO_SIZE[1] - 120, 'white', 'black'
                )
            
            # 保存处理后的图片
            output_path = image_path.replace('.jpg', '_with_subtitle.jpg')
            img.save(output_path, 'JPEG', quality=95)
            return output_path
            
        except Exception as e:
            logger.error(f"添加字幕失败: {e}")
            return image_path  # 返回原图片
    
    def _draw_text_with_outline(self, draw: ImageDraw.Draw, text: str, font: ImageFont.ImageFont,
                              x: int, y: int, fill_color: str, outline_color: str):
        """绘制带边框的文字"""
        # 获取文字尺寸
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # 计算居中位置
        text_x = x - text_width // 2
        text_y = y - text_height // 2
        
        # 绘制边框（在多个位置绘制黑色文字）
        outline_width = 2
        for dx in range(-outline_width, outline_width + 1):
            for dy in range(-outline_width, outline_width + 1):
                if dx != 0 or dy != 0:
                    draw.text((text_x + dx, text_y + dy), text, font=font, fill=outline_color)
        
        # 绘制主文字
        draw.text((text_x, text_y), text, font=font, fill=fill_color)
    
    def create_image_clip(self, resource: MediaResource, global_subtitle: Optional[GlobalSubtitle] = None) -> VideoFileClip:
        """创建图片视频片段"""
        try:
            # 下载图片
            filename = f"image_{resource.order}_{int(os.urandom(4).hex(), 16)}.jpg"
            image_path = self.download_image(resource.url, filename)
            
            # 添加字幕
            if resource.subtitle or global_subtitle:
                image_path = self.add_subtitle_to_image(image_path, resource.subtitle or "", global_subtitle)
            
            # 确定显示时长
            duration = resource.duration if resource.duration and resource.duration > 0 else self.DEFAULT_IMAGE_DURATION
            
            # 创建视频片段
            clip = ImageClip(image_path, duration=duration)
            clip = clip.resize(self.VIDEO_SIZE)
            clip = clip.set_fps(self.VIDEO_FPS)
            
            logger.info(f"创建图片视频片段：{filename}, 时长：{duration}秒")
            return clip
            
        except Exception as e:
            logger.error(f"创建图片片段失败: {e}")
            raise
    
    def process_video_clip(self, resource: MediaResource) -> VideoFileClip:
        """处理视频片段"""
        try:
            # 对于视频资源，直接使用URL（假设是本地路径或可访问的URL）
            clip = VideoFileClip(resource.url)
            
            # 调整尺寸和帧率
            clip = clip.resize(self.VIDEO_SIZE)
            clip = clip.set_fps(self.VIDEO_FPS)
            
            # 如果指定了时长，则截取
            if resource.duration and resource.duration > 0:
                clip = clip.subclip(0, min(resource.duration, clip.duration))
            
            logger.info(f"处理视频片段：{resource.url}, 时长：{clip.duration}秒")
            return clip
            
        except Exception as e:
            logger.error(f"处理视频片段失败: {e}")
            raise

    def generate_main_video(self, media_resources: List[MediaResource],
                          global_subtitle: Optional[GlobalSubtitle] = None) -> VideoFileClip:
        """生成主视频（无音频）"""
        try:
            logger.info("开始生成主视频（无音频）")
            logger.info(f"媒体资源数量：{len(media_resources)}")

            # 过滤和排序视觉资源
            visual_resources = [r for r in media_resources if r.type in [1, 2]]
            visual_resources.sort(key=lambda x: x.order if x.order is not None else 0)

            if not visual_resources:
                raise ValueError("至少需要一个图片或视频资源")

            logger.info(f"视觉资源数量：{len(visual_resources)}")

            # 分析总时长
            total_duration = 0
            for resource in visual_resources:
                if resource.type == 1:  # 图片
                    duration = resource.duration if resource.duration and resource.duration > 0 else self.DEFAULT_IMAGE_DURATION
                    total_duration += duration
                    logger.info(f"图片时长：{duration}秒")
                elif resource.type == 2:  # 视频
                    # 对于视频，需要获取实际时长
                    total_duration += 5  # 临时假设5秒
                    logger.info(f"视频时长：假设5秒")

            logger.info(f"预计总时长：{total_duration}秒")

            # 创建视频片段
            clips = []
            for resource in visual_resources:
                if resource.type == 1:  # 图片
                    clip = self.create_image_clip(resource, global_subtitle)
                elif resource.type == 2:  # 视频
                    clip = self.process_video_clip(resource)
                else:
                    continue

                clips.append(clip)

            # 拼接所有片段
            if len(clips) == 1:
                main_video = clips[0]
            else:
                main_video = concatenate_videoclips(clips, method="compose")

            logger.info(f"主视频生成成功（无音频），时长：{main_video.duration}秒")
            return main_video

        except Exception as e:
            logger.error(f"生成主视频失败: {e}")
            raise

    def create_background_audio(self, audio_path: str, target_duration: float) -> AudioFileClip:
        """创建循环背景音乐"""
        try:
            if not os.path.exists(audio_path):
                logger.warning(f"背景音乐文件不存在：{audio_path}")
                # 创建静音音频
                return AudioFileClip(None, duration=target_duration)

            # 加载原始音频
            original_audio = AudioFileClip(audio_path)
            original_duration = original_audio.duration

            logger.info(f"背景音乐处理 - 原始：{original_duration:.1f}秒，目标：{target_duration:.1f}秒")

            if original_duration >= target_duration:
                # 音频足够长，直接截取
                logger.info(f"音频足够长，截取到{target_duration:.1f}秒")
                return original_audio.subclip(0, target_duration)
            else:
                # 音频需要循环
                full_copies = int(target_duration // original_duration)
                remaining_time = target_duration % original_duration

                logger.info(f"音频循环方案：{full_copies}个完整音频 + {remaining_time:.1f}秒剩余")

                # 创建循环音频片段列表
                audio_clips = []

                # 添加完整的音频副本
                for i in range(full_copies):
                    audio_clips.append(original_audio)

                # 添加剩余部分
                if remaining_time > 0:
                    audio_clips.append(original_audio.subclip(0, remaining_time))

                # 拼接音频
                if len(audio_clips) == 1:
                    return audio_clips[0]
                else:
                    return concatenate_audioclips(audio_clips)

        except Exception as e:
            logger.error(f"创建背景音乐失败: {e}")
            raise

    def generate_video(self, media_resources: List[MediaResource],
                      background_audio_path: str, end_video_path: str,
                      output_path: str, global_subtitle: Optional[GlobalSubtitle] = None) -> str:
        """生成完整视频"""
        try:
            logger.info("=== 开始视频生成 ===")

            # 1. 生成主视频（无音频）
            main_video = self.generate_main_video(media_resources, global_subtitle)
            main_duration = main_video.duration

            # 2. 加载结尾视频
            if not os.path.exists(end_video_path):
                raise FileNotFoundError(f"结尾视频文件不存在：{end_video_path}")

            end_video = VideoFileClip(end_video_path)
            end_video = end_video.resize(self.VIDEO_SIZE).set_fps(self.VIDEO_FPS)
            end_duration = end_video.duration

            logger.info(f"时长信息 - 主视频：{main_duration:.1f}秒，结尾视频：{end_duration:.1f}秒")

            # 3. 创建音频轨道
            # 主视频音频：循环背景音乐
            main_audio = self.create_background_audio(background_audio_path, main_duration)

            # 结尾视频音频：保持原有音频
            end_audio = end_video.audio if end_video.audio else AudioFileClip(None, duration=end_duration)

            # 4. 为视频添加音频
            main_video_with_audio = main_video.set_audio(main_audio)
            end_video_with_audio = end_video.set_audio(end_audio)

            # 5. 拼接最终视频
            logger.info("拼接最终视频")
            final_video = concatenate_videoclips([main_video_with_audio, end_video_with_audio], method="compose")

            # 6. 输出视频
            logger.info(f"开始输出视频到：{output_path}")
            final_video.write_videofile(
                output_path,
                fps=self.VIDEO_FPS,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=os.path.join(self.temp_dir, 'temp_audio.m4a'),
                remove_temp=True,
                verbose=False,
                logger=None  # 禁用moviepy的详细日志
            )

            # 7. 验证结果
            final_duration = final_video.duration
            expected_duration = main_duration + end_duration
            logger.info(f"视频生成完成 - 总时长：{final_duration:.1f}秒（预期：{expected_duration:.1f}秒）")

            # 清理资源
            main_video.close()
            end_video.close()
            main_audio.close()
            end_audio.close()
            final_video.close()

            logger.info("=== 视频生成完成 ===")
            return output_path

        except Exception as e:
            logger.error(f"视频生成失败: {e}")
            raise


def create_test_data() -> Tuple[List[MediaResource], GlobalSubtitle]:
    """创建测试数据"""
    # 媒体资源
    media_resources = [
        MediaResource(
            type=1, order=1,
            url="https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/4a623d7c850b49ed9f57bdb14d6b7809.jpg",
            subtitle="进店前 - 伊珊娜精致洗护"
        ),
        MediaResource(
            type=1, order=2,
            url="https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/29bb4b584ca144a381e62dfd7e94ea1c.jpg",
            subtitle="洗护中 - 专业护理"
        ),
        MediaResource(
            type=1, order=3,
            url="https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/e3432e85bf324d82b74902c5e8d6e355.jpg",
            subtitle="美容中 - 精心打理"
        ),
        MediaResource(
            type=1, order=4,
            url="https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/d0a9a2c535754f52b8f086307ac99e01.jpg",
            subtitle="护理中 - 细致呵护"
        ),
        MediaResource(
            type=1, order=5,
            url="https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/a9fec78b4eab4be8ace2ecb387e2a114.jpg",
            subtitle="完成后 - 焕然一新"
        ),
        MediaResource(
            type=2, order=6,
            url="https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/a47a0aba77714df2868a93fef9cc79a1.mp4"
        ),
        MediaResource(
            type=1, order=7,
            url="https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/ff81883e74054393a2a7e9aebcb2b70a.jpg",
            subtitle="服务完成 - 满意离店"
        )
    ]

    # 全局字幕
    global_subtitle = GlobalSubtitle(
        title="贵阳宠物洗护新模式",
        content="洗护、美容、寄养消费都可以积分，积分可兑换，欢迎您的光临！"
    )

    return media_resources, global_subtitle


def main():
    """主函数 - 使用示例"""
    try:
        # 创建视频生成器
        generator = VideoGenerator()

        # 创建测试数据
        media_resources, global_subtitle = create_test_data()

        # 配置路径
        background_audio_path = "C:/Users/<USER>/desktop/extracted_audio_1748591297575.aac"
        end_video_path = "D:/WorkSpace/web/generateVideo-master/ruoyi-common/target/classes/templates/宠孩生活结尾.mp4"
        output_path = "C:/Users/<USER>/desktop/generated_video_python.mp4"

        # 生成视频
        result_path = generator.generate_video(
            media_resources=media_resources,
            background_audio_path=background_audio_path,
            end_video_path=end_video_path,
            output_path=output_path,
            global_subtitle=global_subtitle
        )

        print(f"✅ 视频生成成功：{result_path}")

    except Exception as e:
        logger.error(f"❌ 视频生成失败：{e}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
