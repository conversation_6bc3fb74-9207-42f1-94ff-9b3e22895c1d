# 视频生成音频播放优化说明

## 问题描述
原来的实现中，背景音乐会循环播放填满整个视频的总时长（主视频 + 结尾视频），导致结尾视频也播放背景音乐，而不是专门的结尾音乐。

## 解决方案
修改了 `VideoGenerateUtils` 类，新增了支持结尾音乐的功能：

### 1. 新增方法
```java
// 支持结尾音乐的方法
public static File generateVideo(List<MediaResourceVo> mediaResources, String outputPath,
                                GlobalSubtitleVO globalSubtitle, String audioPath, String endingAudioPath)

// 支持结尾音乐的拼接方法
public static File concatEndVideo(String mainVideoPath, String endVideoPath, String outputPath, 
                                 String audioPath, String endingAudioPath)
```

### 2. 实现逻辑
1. **主视频音频处理**：
   - 如果提供了背景音乐，将背景音乐循环播放以匹配主视频时长
   - 将背景音乐添加到主视频中

2. **结尾视频音频处理**：
   - 如果提供了结尾音乐，将结尾音乐循环播放以匹配结尾视频时长
   - 将结尾音乐添加到结尾视频中

3. **视频拼接**：
   - 标准化两个视频的格式
   - 使用 FFmpeg 的 concat 功能拼接视频
   - 保持各自的音频轨道

### 3. 使用方法

#### 方法一：只使用背景音乐（保持原有功能）
```java
VideoGenerateUtils.generateVideo(resources, outputPath, globalSubtitle, audioPath);
```

#### 方法二：使用背景音乐 + 结尾音乐（新功能）
```java
String backgroundAudioPath = "path/to/background_music.aac";
String endingAudioPath = "path/to/ending_music.aac";

VideoGenerateUtils.generateVideo(resources, outputPath, globalSubtitle, 
                                backgroundAudioPath, endingAudioPath);
```

### 4. 参数说明
- `audioPath`: 背景音乐路径，用于主视频部分
- `endingAudioPath`: 结尾音乐路径，用于结尾视频部分
- 两个音频参数都可以为 `null`，系统会自动处理

### 5. 音频文件要求
- 支持常见音频格式：AAC、MP3、WAV 等
- 音频会自动循环以匹配对应视频段的时长
- 建议使用 AAC 格式以获得最佳兼容性

### 6. 示例代码
参考 `VideoGeneratorUtilTest.java` 中的示例：

```java
// 设置音频路径
String audioPath = "C:\\path\\to\\background_music.aac";
String endingAudioPath = "C:\\path\\to\\ending_music.aac";

// 生成视频
VideoGenerateUtils.generateVideo(resources, outputPath, globalSubtitle, 
                                audioPath, endingAudioPath);
```

## 注意事项
1. 确保音频文件存在且可读
2. 系统会自动清理生成过程中的临时文件
3. 如果不需要结尾音乐，可以传入 `null` 或使用原有的方法
4. 音频处理过程中会有日志输出，便于调试和监控

## 兼容性
- 保持了原有 API 的兼容性
- 新增的方法是可选的，不影响现有代码
- 支持向后兼容，原有调用方式仍然有效
